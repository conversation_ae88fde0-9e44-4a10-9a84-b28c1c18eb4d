# B站网址结构分析报告

## 概述

通过分析B站网址.txt文件中的URL样本，可以发现B站网站主要包含以下几种页面类型：

1. 主页
2. 搜索页面（细分为综合区、视频区、专栏区、用户区）
3. 视频详情页

每种搜索页面还有不同的排序方式，形成了丰富的URL结构。

## URL结构特点

### 1. B站主页

```
https://www.bilibili.com/
```

特点：简单的域名根路径。

### 2. B站搜索页面

搜索页面根据内容类型分为四个主要区域：

#### 2.1 综合区搜索

基本格式：`https://search.bilibili.com/all?keyword={关键词}&search_source=1`

排序方式：
- 综合排序：无额外参数或`order`参数缺失
- 最多播放：`order=click`
- 最新发布：`order=pubdate`
- 最多弹幕：`order=dm`
- 最多收藏：`order=stow`

示例：
```
https://search.bilibili.com/all?keyword=windsurf&search_source=1
https://search.bilibili.com/all?keyword=windsurf&search_source=1&order=click
```

#### 2.2 视频区搜索

基本格式：`https://search.bilibili.com/video?keyword={关键词}&search_source=1`

排序方式：
- 综合排序：无额外参数或`order`参数缺失
- 最多播放：`order=click`
- 最新发布：`order=pubdate`
- 最多弹幕：`order=dm`
- 最多收藏：`order=stow`

示例：
```
https://search.bilibili.com/video?keyword=%E9%98%BF%E5%A8%87&search_source=1
https://search.bilibili.com/video?keyword=coze%E5%B7%A5%E4%BD%9C%E6%B5%81&search_source=1&order=click
```

#### 2.3 专栏区搜索

基本格式：`https://search.bilibili.com/article?keyword={关键词}&search_source=1`

排序方式：
- 综合排序：无额外参数或`order`参数缺失
- 最新发布：`order=pubdate`
- 最多点击：`order=click`
- 最多喜欢：`order=attention`
- 最多评论：`order=scores`

示例：
```
https://search.bilibili.com/article?keyword=coze&search_source=1
https://search.bilibili.com/article?keyword=coze&search_source=1&order=pubdate
```

#### 2.4 用户区搜索

基本格式：`https://search.bilibili.com/upuser?keyword={关键词}&search_source=1`

排序方式：
- 默认排序：无额外参数或`order`参数缺失
- 粉丝数由高到低：`order=fans`
- 粉丝数由低到高：`order=fans&order_sort=1`
- Lv等级由高到低：`order=level`
- Lv等级由低到高：`order=level&order_sort=1`

示例：
```
https://search.bilibili.com/upuser?keyword=coze&search_source=1
https://search.bilibili.com/upuser?keyword=coze&search_source=1&order=fans
```

### 3. 视频详情页

基本格式：`https://www.bilibili.com/video/{视频ID}/`

视频ID通常以BV开头，后跟一串字母和数字。URL可能包含额外的查询参数，如`spm_id_from`和`vd_source`等。

示例：
```
https://www.bilibili.com/video/BV1wd56zMEiE/?spm_id_from=333.337.search-card.all.click&vd_source=26fecd036ca8ee33432c6eef9fa8f939
https://www.bilibili.com/video/BV1Ap5WzdEYz/
```

## 识别规则

基于以上分析，可以制定以下识别规则：

1. 主页识别：URL为`https://www.bilibili.com/`

2. 搜索页识别：
   - 综合区：URL包含`search.bilibili.com/all`
   - 视频区：URL包含`search.bilibili.com/video`
   - 专栏区：URL包含`search.bilibili.com/article`
   - 用户区：URL包含`search.bilibili.com/upuser`

3. 排序方式识别：
   - 根据URL中的`order`参数值判断
   - 对于用户区，还需检查`order_sort`参数

4. 视频详情页识别：URL包含`www.bilibili.com/video/`

## 结论

B站的URL结构清晰，通过分析URL的域名、路径和查询参数，可以准确识别页面类型和排序方式。这些规则可以用于实现自动识别B站页面类型的功能。