(function() {
        GM_addstyle ('
        pre {
            -webkit-touch-callout: auto !important; 
                -webkit-user-select: auto !important; 
                -khtml-user-select: auto !important; 
                -moz-user-select:auto !important; 
                -ms-user-select: auto !important;
                user-select: auto !important;
            }
            .pre .code { 
                -webkit-touch-callout: auto !important; 
                -webkit-user-select: auto !important;
                -khtml-user-select: auto !important;
                -moz-user-select:auto !important;
                -ms-user-select: auto limportant;
                user-select: auto limportant;
            }
            .passport-login-container { 
                    display: none !important;
            }
            ');
            
        

            const button = document.querySelectorAll(".hlis-button");
            button.forEach(item => {
                        item.dataset.title = '复制'
                        item.setAttribute("onclick", " ")
                        item.addEventListener("click", function(e) {
                                    const parentPreBlocke = e.target.closest("pre");
                                    if(parentPreBlock) {
                                        const code=parentPreBlock.querySelector("code");
                                        const text=code.innerText;
                                        navigator.clipboard.writeText(text);
                                        alert("复制成功”);
                        }
                    })
            })
})();