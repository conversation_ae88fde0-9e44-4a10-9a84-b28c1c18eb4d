# 抖音网址结构分析报告

## 概述

通过对抖音网址分析.txt中的URL进行分析，我们可以识别出抖音平台的不同页面类型及其URL结构特点。这些特点可以用于开发自动识别抖音页面具体类型的功能。

## 页面类型及URL结构分析

### 1. 抖音主页

**URL示例：**
```
https://www.douyin.com
```

**特征：**
- 域名为 `www.douyin.com`
- 路径为根路径 `/`，没有额外参数

### 2. 抖音精选页

**URL示例：**
```
https://www.douyin.com/discover
https://www.douyin.com/discover/knowledge
```

**特征：**
- 包含 `/discover` 路径
- 可能包含子分类，如 `/discover/knowledge`、`/discover/sports` 等
- 常见子分类包括：knowledge(知识)、sports(体育)、car(汽车)、acg(二次元)、game(游戏)、film(影视)、vlog(生活vlog)、travel(旅行)、theater(小剧场)、food(美食)、agriculture(三农)、music(音乐)、animal(动物)、child(亲子)、beauty(美妆)

### 3. 抖音推荐页

**URL示例：**
```
https://www.douyin.com/?recommend=1
```

**特征：**
- 域名为 `www.douyin.com`
- 包含查询参数 `recommend=1`

### 4. 抖音关注页

**URL示例：**
```
https://www.douyin.com/follow
```

**特征：**
- 包含 `/follow` 路径

### 5. 抖音朋友页

**URL示例：**
```
https://www.douyin.com/friend
```

**特征：**
- 包含 `/friend` 路径

### 6. 抖音博主主页

**URL示例：**
```
https://www.douyin.com/user/MS4wLjABAAAAwmU8qyb6PtJXwewRDT-o6XTzb1AeRYhujW8hghA7SlkqKVegnobks9EIsZZHIzLH
```

**特征：**
- 包含 `/user/` 路径，后跟用户ID
- 可能包含 `from_tab_name=main` 参数
- 用户ID通常是一串Base64编码的字符

### 7. 抖音直播页

**URL示例：**
```
https://live.douyin.com/?from_nav=1
https://live.douyin.com/category/1_1
```

**特征：**
- 域名为 `live.douyin.com`
- 可能包含 `/category/` 路径，后跟分类ID
- 常见分类包括：射击游戏(1_1)、竞技游戏(1_2)、单机游戏(1_3)、棋牌游戏(1_4)、休闲益智(1_5)、角色扮演(1_6)、策略卡牌(1_7)、娱乐天地(3_10000)、科技文化(3_10001)

### 8. 抖音放映厅和短剧

**URL示例：**
```
https://www.douyin.com/vs
https://www.douyin.com/series
```

**特征：**
- 放映厅：包含 `/vs` 路径
- 短剧：包含 `/series` 路径

### 9. 抖音搜索页

**URL示例：**
```
https://www.douyin.com/root/search/%E6%8A%A4%E8%82%A4?type=general
https://www.douyin.com/root/search/%E7%BE%8E%E7%99%BD?type=video
```

**特征：**
- 包含 `/root/search/` 路径，后跟URL编码的搜索关键词
- 包含 `type` 参数，常见值有：
  - general：综合搜索
  - video：视频搜索
  - user：用户搜索
  - live：直播搜索
- 可能包含 `aid` 参数（似乎是会话ID）

### 10. 抖音视频详情页

**URL示例：**
```
https://www.douyin.com/video/7492711644468972838
https://v.douyin.com/wP-slbKJsxQ/
https://www.douyin.com/discover/beauty?modal_id=7487942510640123163
```

**特征：**
- 长链接：包含 `/video/` 路径，后跟视频ID（数字）
- 短链接：域名为 `v.douyin.com`，后跟短码
- 模态框形式：包含 `modal_id` 参数
- 可能出现在不同页面中，如发现页、用户页等

## 识别策略

基于以上分析，我们可以采用以下策略来识别抖音页面的具体类型：

1. 首先检查域名（`live.douyin.com` 或 `www.douyin.com` 或 `v.douyin.com`）
2. 然后检查URL路径和查询参数的特定模式
3. 根据匹配的模式确定页面类型

这种方法可以准确识别用户当前浏览的是哪种类型的抖音页面，从而提供更精确的页面类型信息。