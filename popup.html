<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title><PERSON>ie复制工具</title>
  <style>
    :root {
      --primary: #4285f4;
      --primary-light: #e8f0fe;
      --primary-dark: #1a73e8;
      --accent: #ff6b9d;
      --accent-light: #ffe4ec; 
      --text: #202124;
      --text-light: #5f6368;
      --bg: #ffffff;
      --card-bg: #f8f9fa;
      --border: #dadce0;
      --success: #0f9d58;
      --error: #ea4335;
      --warning: #fbbc04;
      --shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
      --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
      --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
      --radius-sm: 4px;
      --radius-md: 8px;
      --radius-lg: 12px;
      --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      --padding-card: 16px;
      
      /* 深色模式变量 */
      --dark-bg: #202124;
      --dark-card-bg: #292a2d;
      --dark-border: #3c4043;
      --dark-text: #e8eaed;
      --dark-text-light: #9aa0a6;
    }
    
    .dark-mode {
      --bg: var(--dark-bg);
      --card-bg: var(--dark-card-bg);
      --border: var(--dark-border);
      --text: var(--dark-text);
      --text-light: var(--dark-text-light);
    }
    
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }
    
    body {
      width: 450px; /* 增加一点宽度 */
      font-family: 'Google Sans', 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      background-color: var(--bg);
      color: var(--text);
      line-height: 1.5;
      transition: var(--transition);
    }
    
    .container {
      padding: 20px;
    }
    
    /* Header Styles */
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
    }
    
    .logo-title {
      display: flex;
      align-items: center;
    }
    
    .logo {
      height: 28px;
      margin-right: 12px;
      color: var(--primary);
    }
    
    .title {
      font-size: 18px;
      font-weight: 500;
      color: var(--text);
    }
    
    .header-controls {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .theme-toggle {
      background: none;
      border: none;
      cursor: pointer;
      color: var(--text-light);
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 5px;
      border-radius: 50%;
      transition: var(--transition);
    }
    
    .theme-toggle:hover {
      background-color: var(--card-bg);
      color: var(--primary);
    }
    
    .theme-icon {
      width: 20px;
      height: 20px;
    }
    
    /* Author Banner */
    .author-section {
      background: linear-gradient(to right, var(--primary-light), var(--accent-light));
      border-radius: var(--radius-lg);
      padding: var(--padding-card);
      margin-bottom: 16px;
      box-shadow: var(--shadow-sm);
      position: relative;
      overflow: hidden;
    }
    
    .author-pattern {
      position: absolute;
      top: 0;
      right: 0;
      width: 100%;
      height: 100%;
      opacity: 0.05;
      background-image: radial-gradient(circle at 10% 20%, var(--accent) 10%, transparent 10.5%),
                        radial-gradient(circle at 30% 70%, var(--primary) 10%, transparent 10.5%),
                        radial-gradient(circle at 55% 35%, var(--accent) 8%, transparent 8.5%),
                        radial-gradient(circle at 70% 80%, var(--primary) 8%, transparent 8.5%),
                        radial-gradient(circle at 90% 40%, var(--accent) 12%, transparent 12.5%);
      z-index: 0;
    }
    
    .author-banner {
      position: relative;
      z-index: 1;
      display: flex;
      align-items: center;
      gap: 12px;
    }
    
    .author-avatar {
      width: 54px;
      height: 54px;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid white;
      box-shadow: var(--shadow-sm);
    }
    
    .author-info {
      flex: 1;
    }
    
    .author-name {
      font-weight: 600;
      color: var(--text);
      font-size: 16px;
      margin-bottom: 4px;
    }
    
    .contacts {
      display: flex;
      gap: 12px;
      margin-top: 8px;
    }
    
    .contact-item {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 13px;
      font-weight: 500;
      padding: 6px 10px;
      border-radius: 20px;
      background-color: white;
      box-shadow: var(--shadow-sm);
      cursor: pointer;
      position: relative;
      transition: var(--transition);
      color: var(--text-light);
    }
    
    .contact-item:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
      color: var(--primary);
    }
    
    .contact-icon {
      width: 14px;
      height: 14px;
    }
    
    .qrcode-tooltip {
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%) translateY(10px);
      background: white;
      padding: 12px;
      border-radius: var(--radius-md);
      box-shadow: var(--shadow-lg);
      z-index: 100;
      opacity: 0;
      visibility: hidden;
      transition: var(--transition);
      width: 160px;
      text-align: center;
    }
    
    .contact-item:hover .qrcode-tooltip {
      opacity: 1;
      visibility: visible;
      transform: translateX(-50%) translateY(5px);
    }
    
    .qrcode-tooltip-img {
      width: 100%;
      height: auto;
      border-radius: var(--radius-sm);
    }
    
    .tooltip-text {
      font-size: 12px;
      color: var(--text-light);
      margin-top: 6px;
    }
    
    /* Cookie Section */
    .main-section {
      background-color: var(--card-bg);
      border-radius: var(--radius-lg);
      padding: var(--padding-card);
      margin-bottom: 16px;
      box-shadow: var(--shadow-sm);
      border: 1px solid var(--border);
    }
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid var(--border);
    }
    
    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--text);
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .section-title-icon {
      width: 18px;
      height: 18px;
      color: var(--primary);
    }
    
    .cookie-stats {
      display: flex;
      gap: 12px;
      margin-bottom: 12px;
    }
    
    .stat-item {
      background-color: white;
      border-radius: var(--radius-md);
      padding: 12px;
      flex: 1;
      text-align: center;
      border: 1px solid var(--border);
    }
    
    .stat-value {
      font-size: 20px;
      font-weight: 600;
      color: var(--primary);
      margin-bottom: 4px;
    }
    
    .stat-label {
      font-size: 12px;
      color: var(--text-light);
    }
    
    .cookie-tools {
      display: flex;
      gap: 8px;
      margin-bottom: 12px;
    }
    
    .tool-action {
      flex: 1;
      background-color: white;
      border: 1px solid var(--border);
      border-radius: var(--radius-md);
      padding: 10px;
      font-size: 13px;
      font-weight: 500;
      color: var(--text);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      cursor: pointer;
      transition: var(--transition);
    }
    
    .tool-action:hover {
      background-color: var(--primary-light);
      border-color: var(--primary-light);
      color: var(--primary);
    }
    
    .tool-icon {
      width: 16px;
      height: 16px;
    }
    
    .cookie-container {
      position: relative;
    }
    
    .cookie-search {
      width: 100%;
      padding: 8px 12px;
      border-radius: var(--radius-md);
      border: 1px solid var(--border);
      margin-bottom: 10px;
      font-size: 13px;
      outline: none;
      transition: var(--transition);
    }
    
    .cookie-search:focus {
      border-color: var(--primary);
      box-shadow: 0 0 0 2px var(--primary-light);
    }
    
    .cookie-content-wrapper {
      position: relative;
      border-radius: var(--radius-md);
      border: 1px solid var(--border);
      overflow: hidden;
    }
    
    #cookieContent {
      height: 180px;
      overflow-y: auto;
      padding: 12px;
      font-family: 'SF Mono', 'Roboto Mono', 'Consolas', 'Monaco', monospace;
      font-size: 13px;
      line-height: 1.6;
      color: var(--text);
      background-color: white;
      white-space: pre-wrap;
      word-break: break-all;
    }
    
    .cookie-item {
      display: flex;
      padding: 6px 8px;
      border-bottom: 1px solid var(--border);
      font-size: 13px;
      transition: var(--transition);
    }
    
    .cookie-item:hover {
      background-color: var(--primary-light);
    }
    
    .cookie-item:last-child {
      border-bottom: none;
    }
    
    .cookie-name {
      font-weight: 500;
      color: var(--primary);
      margin-right: 8px;
    }
    
    .cookie-value {
      color: var(--text-light);
      flex: 1;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
    
    /* Actions */
    .action-button {
      width: 100%;
      padding: 12px 16px;
      background-color: var(--primary);
      color: white;
      border: none;
      border-radius: var(--radius-md);
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      transition: var(--transition);
      box-shadow: var(--shadow-sm);
      margin-bottom: 16px;
    }
    
    .action-button:hover {
      background-color: var(--primary-dark);
      box-shadow: var(--shadow-md);
      transform: translateY(-1px);
    }
    
    .action-button:active {
      transform: translateY(0);
    }
    
    .action-button:disabled {
      background-color: var(--text-light);
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }
    
    .button-icon {
      width: 18px;
      height: 18px;
    }
    
    #status {
      padding: 12px;
      border-radius: var(--radius-md);
      text-align: center;
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 16px;
      display: none;
      animation: fadeIn 0.3s ease;
    }
    
    .status-success {
      background-color: #e6f4ea;
      color: var(--success);
      border: 1px solid #ceead6;
    }
    
    .status-error {
      background-color: #fce8e6;
      color: var(--error);
      border: 1px solid #fadbd8;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(-10px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    /* QR Code Section */
    .qrcode-section {
      background-color: var(--card-bg);
      border-radius: var(--radius-lg);
      padding: var(--padding-card);
      margin-bottom: 16px;
      text-align: center;
      box-shadow: var(--shadow-sm);
      border: 1px solid var(--border);
    }
    
    .qrcode-title {
      font-size: 15px;
      font-weight: 600;
      color: var(--text);
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }
    
    .qrcode-img {
      width: 140px;
      height: 140px;
      margin: 0 auto 12px;
      border-radius: var(--radius-md);
      padding: 8px;
      background-color: white;
      box-shadow: var(--shadow-sm);
    }
    
    .qrcode-description {
      font-size: 13px;
      color: var(--text-light);
    }
    
    .highlight {
      color: var(--primary);
      font-weight: 600;
    }
    
    /* Footer */
    .footer {
      text-align: center;
      font-size: 12px;
      color: var(--text-light);
      padding: 8px 0;
      margin-top: 8px;
    }
    
    /* Badges */
    .badge {
      display: inline-block;
      padding: 2px 6px;
      border-radius: 12px;
      font-size: 10px;
      font-weight: 600;
      margin-left: 6px;
    }
    
    .badge-primary {
      background-color: var(--primary-light);
      color: var(--primary);
    }
    
    .badge-accent {
      background-color: var(--accent-light);
      color: var(--accent);
    }
    
    /* 页面类型显示样式 */
    .page-type-info {
      display: flex;
      align-items: center;
      gap: 6px;
    }
    
    .page-type-icon {
      width: 18px;
      height: 18px;
      color: var(--primary);
    }
    
    #pageTypeDisplay {
      font-size: 13px;
      font-weight: 500;
      color: var(--text);
    }
    
    /* Mode Tabs */
    .mode-tabs {
      display: flex;
      border-bottom: 1px solid var(--border);
      margin-bottom: 12px;
    }
    
    .mode-tab {
      padding: 8px 12px;
      font-size: 13px;
      font-weight: 500;
      color: var(--text-light);
      cursor: pointer;
      border-bottom: 2px solid transparent;
      transition: var(--transition);
    }
    
    .mode-tab.active {
      color: var(--primary);
      border-bottom-color: var(--primary);
    }
    
    .mode-tab:hover:not(.active) {
      color: var(--text);
      background-color: var(--card-bg);
    }
    
    /* Cookie Table View */
    .table-view {
      display: none;
      border: 1px solid var(--border);
      border-radius: var(--radius-md);
      overflow: hidden;
      margin-bottom: 12px;
    }
    
    .table-header {
      display: grid;
      grid-template-columns: minmax(100px, 1fr) minmax(150px, 2fr) 80px;
      padding: 8px 12px;
      background-color: var(--card-bg);
      border-bottom: 1px solid var(--border);
      font-weight: 500;
      font-size: 12px;
      color: var(--text-light);
    }
    
    .table-body {
      max-height: 180px;
      overflow-y: auto;
      background-color: white;
    }
    
    .cookie-row {
      display: grid;
      grid-template-columns: minmax(100px, 1fr) minmax(150px, 2fr) 80px;
      padding: 8px 12px;
      border-bottom: 1px solid var(--border);
      font-size: 13px;
      transition: var(--transition);
    }
    
    .cookie-row:last-child {
      border-bottom: none;
    }
    
    .cookie-row:hover {
      background-color: var(--primary-light);
    }
    
    .cookie-cell {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding-right: 8px;
    }
    
    .cookie-name-cell {
      font-weight: 500;
      color: var(--primary);
    }
    
    .cookie-value-cell {
      color: var(--text-light);
    }
    
    .cookie-actions-cell {
      display: flex;
      justify-content: flex-end;
      gap: 4px;
    }
    
    .cookie-action {
      border: none;
      background: none;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      cursor: pointer;
      color: var(--text-light);
      transition: var(--transition);
    }
    
    .cookie-action:hover {
      background-color: var(--card-bg);
      color: var(--primary);
    }
    
    .edit-icon, .delete-icon, .copy-icon {
      width: 16px;
      height: 16px;
    }
    
    /* Cookie Edit Modal */
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
      opacity: 0;
      visibility: hidden;
      transition: var(--transition);
    }
    
    .modal-overlay.active {
      opacity: 1;
      visibility: visible;
    }
    
    .modal {
      background-color: var(--bg);
      border-radius: var(--radius-lg);
      box-shadow: var(--shadow-lg);
      width: 350px;
      max-width: 90%;
      transform: translateY(-20px);
      transition: var(--transition);
    }
    
    .modal-overlay.active .modal {
      transform: translateY(0);
    }
    
    .modal-header {
      padding: 16px;
      border-bottom: 1px solid var(--border);
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    
    .modal-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--text);
    }
    
    .modal-close {
      background: none;
      border: none;
      color: var(--text-light);
      cursor: pointer;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: var(--transition);
    }
    
    .modal-close:hover {
      background-color: var(--card-bg);
      color: var(--text);
    }
    
    .modal-body {
      padding: 16px;
    }
    
    .form-group {
      margin-bottom: 16px;
    }
    
    .form-label {
      display: block;
      font-size: 13px;
      font-weight: 500;
      color: var(--text);
      margin-bottom: 6px;
    }
    
    .form-input {
      width: 100%;
      padding: 8px 12px;
      border-radius: var(--radius-md);
      border: 1px solid var(--border);
      font-size: 13px;
      outline: none;
      transition: var(--transition);
      background-color: var(--bg);
      color: var(--text);
    }
    
    .form-input:focus {
      border-color: var(--primary);
      box-shadow: 0 0 0 2px var(--primary-light);
    }
    
    .form-textarea {
      resize: vertical;
      min-height: 80px;
    }
    
    .modal-footer {
      padding: 16px;
      border-top: 1px solid var(--border);
      display: flex;
      justify-content: flex-end;
      gap: 8px;
    }
    
    .modal-button {
      padding: 8px 16px;
      border-radius: var(--radius-md);
      font-size: 13px;
      font-weight: 500;
      cursor: pointer;
      transition: var(--transition);
    }
    
    .button-secondary {
      background-color: var(--card-bg);
      color: var(--text);
      border: 1px solid var(--border);
    }
    
    .button-secondary:hover {
      background-color: var(--border);
    }
    
    .button-primary {
      background-color: var(--primary);
      color: white;
      border: none;
      box-shadow: var(--shadow-sm);
    }
    
    .button-primary:hover {
      background-color: var(--primary-dark);
      box-shadow: var(--shadow-md);
    }
    
    /* JSON View */
    .json-view {
      display: none;
      height: 180px;
      overflow-y: auto;
      padding: 12px;
      font-family: 'SF Mono', 'Roboto Mono', 'Consolas', 'Monaco', monospace;
      font-size: 13px;
      line-height: 1.6;
      color: var(--text);
      background-color: white;
      white-space: pre-wrap;
      word-break: break-all;
      border: 1px solid var(--border);
      border-radius: var(--radius-md);
    }
    
    /* 飞书多维表格参数样式 */
    .feishu-params {
      margin-top: 16px;
      padding: 18px 20px 10px 20px;
      background: linear-gradient(135deg, #f5f8fe 60%, #e8f0fe 100%);
      border-radius: 14px;
      border: 1.5px solid var(--primary-light);
      box-shadow: 0 2px 8px rgba(66,133,244,0.07);
      display: flex;
      flex-direction: column;
      gap: 10px;
      min-width: 0;
    }
    .feishu-params .param-item {
      display: flex;
      align-items: center;
      gap: 10px;
      font-size: 14px;
      font-family: 'SF Mono', 'Roboto Mono', 'Consolas', 'Monaco', monospace;
      background: rgba(255,255,255,0.7);
      border-radius: 8px;
      padding: 6px 10px;
      transition: box-shadow 0.2s;
      min-width: 0;
    }
    .feishu-params .param-label {
      color: var(--primary);
      font-weight: 600;
      min-width: 80px;
      letter-spacing: 0.5px;
      flex-shrink: 0;
    }
    .feishu-params .param-value {
      flex: 1;
      color: #222;
      background: none;
      font-size: 14px;
      border: none;
      outline: none;
      padding: 0 2px;
      border-radius: 4px;
      transition: background 0.2s;
      cursor: pointer;
      user-select: all;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .feishu-params .param-value:active,
    .feishu-params .param-value:focus {
      background: #e8f0fe;
    }
    .feishu-params .copy-param {
      margin-left: 6px;
      padding: 3px 10px 3px 7px;
      background: var(--primary);
      color: #fff;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 13px;
      display: flex;
      align-items: center;
      gap: 3px;
      transition: background 0.2s, box-shadow 0.2s;
      box-shadow: 0 1px 3px rgba(66,133,244,0.08);
    }
    .feishu-params .copy-param:hover {
      background: var(--primary-dark);
      box-shadow: 0 2px 8px rgba(66,133,244,0.13);
    }
    .feishu-params .copy-icon {
      width: 14px;
      height: 14px;
      fill: currentColor;
    }
    .dark-mode .feishu-params {
      background: linear-gradient(135deg, #232a3a 60%, #1a2332 100%);
      border-color: #2a3550;
      box-shadow: 0 2px 8px rgba(66,133,244,0.13);
    }
    .dark-mode .feishu-params .param-item {
      background: rgba(34,40,55,0.7);
    }
    .dark-mode .feishu-params .param-label {
      color: #8ab4f8;
    }
    .dark-mode .feishu-params .param-value {
      color: #e8eaed;
    }
    .feishu-param-btn {
      display: flex;
      align-items: center;
      gap: 4px;
      background: var(--primary-light);
      color: var(--primary);
      border: none;
      border-radius: 6px;
      padding: 6px 12px;
      font-size: 13px;
      font-weight: 500;
      margin-right: 8px;
      cursor: pointer;
      transition: background 0.2s, color 0.2s, box-shadow 0.2s;
      box-shadow: 0 1px 3px rgba(66,133,244,0.08);
    }
    .feishu-param-btn:last-child {
      margin-right: 0;
    }
    .feishu-param-btn:hover {
      background: var(--primary);
      color: #fff;
    }
    .feishu-param-btn .copy-icon {
      width: 14px;
      height: 14px;
      fill: currentColor;
    }
    .dark-mode .feishu-param-btn {
      background: #232a3a;
      color: #8ab4f8;
    }
    .dark-mode .feishu-param-btn:hover {
      background: #4285f4;
      color: #fff;
    }
    .feishu-params-area {
      margin-bottom: 10px;
      padding: 10px 14px 6px 14px;
      background: #f5f8fe;
      border-radius: 10px;
      border: 1px solid var(--primary-light);
      box-shadow: 0 1px 4px rgba(66,133,244,0.06);
    }
    .feishu-param-row {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 13px;
      margin-bottom: 4px;
    }
    .feishu-param-row:last-child {
      margin-bottom: 0;
    }
    .feishu-param-label {
      color: var(--primary);
      font-weight: 600;
      min-width: 70px;
    }
    .feishu-param-value {
      flex: 1;
      color: #222;
      font-family: 'SF Mono', 'Roboto Mono', 'Consolas', 'Monaco', monospace;
      background: none;
      border-radius: 4px;
      padding: 0 2px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .feishu-param-copy-btn {
      background: var(--primary-light);
      color: var(--primary);
      border: none;
      border-radius: 5px;
      padding: 3px 10px;
      font-size: 12px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 2px;
      transition: background 0.2s, color 0.2s;
    }
    .feishu-param-copy-btn:hover {
      background: var(--primary);
      color: #fff;
    }
    .feishu-param-copy-btn .copy-icon {
      width: 13px;
      height: 13px;
      fill: currentColor;
    }
    .dark-mode .feishu-params-area {
      background: #232a3a;
      border-color: #2a3550;
      box-shadow: 0 1px 4px rgba(66,133,244,0.13);
    }
    .dark-mode .feishu-param-label {
      color: #8ab4f8;
    }
    .dark-mode .feishu-param-value {
      color: #e8eaed;
    }
    .dark-mode .feishu-param-copy-btn {
      background: #232a3a;
      color: #8ab4f8;
    }
    .dark-mode .feishu-param-copy-btn:hover {
      background: #4285f4;
      color: #fff;
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- Header -->
    <div class="header">
      <div class="logo-title">
        <svg class="logo" viewBox="0 0 24 24">
          <path fill="currentColor" d="M12,3A9,9 0 0,0 3,12A9,9 0 0,0 12,21A9,9 0 0,0 21,12C21,11.5 20.96,11 20.87,10.5C20.6,10 20,10 20,10H18V9C18,8 17,8 17,8H15V7C15,6 14,6 14,6H13V4C13,3 12,3 12,3M9.5,6A1.5,1.5 0 0,1 11,7.5A1.5,1.5 0 0,1 9.5,9A1.5,1.5 0 0,1 8,7.5A1.5,1.5 0 0,1 9.5,6M6.5,10A1.5,1.5 0 0,1 8,11.5A1.5,1.5 0 0,1 6.5,13A1.5,1.5 0 0,1 5,11.5A1.5,1.5 0 0,1 6.5,10M11.5,11A1.5,1.5 0 0,1 13,12.5A1.5,1.5 0 0,1 11.5,14A1.5,1.5 0 0,1 10,12.5A1.5,1.5 0 0,1 11.5,11M16.5,13A1.5,1.5 0 0,1 18,14.5A1.5,1.5 0 0,1 16.5,16H16.5A1.5,1.5 0 0,1 15,14.5H15A1.5,1.5 0 0,1 16.5,13M11,16A1.5,1.5 0 0,1 12.5,17.5A1.5,1.5 0 0,1 11,19A1.5,1.5 0 0,1 9.5,17.5A1.5,1.5 0 0,1 11,16Z" />
        </svg>
        <h1 class="title">Cookie复制工具 <span class="badge badge-primary">v2.2</span></h1>
      </div>
      <div class="header-controls">
        <button id="themeToggle" class="theme-toggle">
          <svg class="theme-icon" viewBox="0 0 24 24">
            <path fill="currentColor" d="M7.5,2C5.71,3.15 4.5,5.18 4.5,7.5C4.5,9.82 5.71,11.85 7.5,13C4.91,13 2.5,10.59 2.5,8C2.5,5.41 4.91,3 7.5,3M19.07,3.5C20.21,4.66 20.5,6.03 20.5,7.5C20.5,12.28 16.72,16 12,16C11.5,16 11.03,15.97 10.56,15.91C10.73,15.28 10.84,14.63 10.91,13.97C11.03,14.42 11.5,14.8 12,14.8C13.81,14.8 15.5,13.42 15.5,12C15.5,10.58 13.81,9.2 12,9.2C11.43,9.2 10.88,9.35 10.39,9.61C9.88,8.93 9.59,8.08 9.54,7.16C8.88,7.05 8.21,7 7.5,7C7.35,7 7.21,7.01 7.06,7.02C7.1,6.86 7.13,6.69 7.13,6.5C7.13,4.47 5.63,2.74 3.68,2.35C4.17,2.14 4.59,2 5,2C5.92,2 6.74,2.5 7.5,2M19.07,17C19.68,17 20.25,16.73 20.66,16.32C20.26,16.69 19.71,16.89 19.07,17M12,22C14.05,20.89 15.71,19.08 16.5,17C13.93,15.95 12,13.81 12,11.5C12,9.19 13.93,7.05 16.5,6C15.54,3.5 13.5,2 12,2C16.42,2 20,5.58 20,10C20,14.42 16.42,18 12,18C10.39,18 8.9,17.5 7.74,16.61C8.73,18.95 10.28,20.85 12,22Z" />
          </svg>
        </button>
      </div>
    </div>
    
    <!-- Author Section -->
    <div class="author-section">
      <div class="author-pattern"></div>
      <div class="author-banner">
        <img src="icons/touxiang.png" class="author-avatar" alt="开发者头像">
        <div class="author-info">
          <div class="author-name">姚安 · AI工具开发者</div>
          <div class="contacts">
            <div class="contact-item" id="wechatContact">
              <svg class="contact-icon" viewBox="0 0 24 24">
                <path fill="currentColor" d="M12,2C6.48,2 2,6.48 2,12C2,17.52 6.48,22 12,22C17.52,22 22,17.52 22,12C22,6.48 17.52,2 12,2M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M16.68,8.68C15.27,10.07 12.53,12.81 11.32,11.68C10.07,10.47 12.73,7.73 14.32,6.32C15.53,7.53 16.47,8.47 16.68,8.68M7.32,15.32C8.73,13.93 11.47,11.19 12.68,12.32C13.93,13.53 11.27,16.27 9.68,17.68C8.47,16.47 7.53,15.53 7.32,15.32Z" />
              </svg>
              <span>cyanlis8</span>
              <div class="qrcode-tooltip">
                <img src="icons/weixin.png" class="qrcode-tooltip-img" alt="微信二维码">
                <div class="tooltip-text">扫码添加微信</div>
              </div>
            </div>
            <div class="contact-item" id="officialAccountContact">
              <svg class="contact-icon" viewBox="0 0 24 24">
                <path fill="currentColor" d="M2.5,18.5C2.5,20.43 4.07,22 6,22H18C19.93,22 21.5,20.43 21.5,18.5V5.5C21.5,3.57 19.93,2 18,2H6C4.07,2 2.5,3.57 2.5,5.5V18.5M20,12L12,17L4,12V6L12,11L20,6V12Z" />
              </svg>
              <span>姚安笔记</span>
              <div class="qrcode-tooltip">
                <img src="icons/gongzhonghao.jpg" class="qrcode-tooltip-img" alt="公众号二维码">
                <div class="tooltip-text">扫码关注公众号</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Cookie Function Section -->
    <div class="main-section">
      <div class="section-header">
        <div class="section-title">
          <svg class="section-title-icon" viewBox="0 0 24 24">
            <path fill="currentColor" d="M12,3A9,9 0 0,0 3,12A9,9 0 0,0 12,21A9,9 0 0,0 21,12C21,11.5 20.96,11 20.87,10.5C20.6,10 20,10 20,10H18V9C18,8 17,8 17,8H15V7C15,6 14,6 14,6H13V4C13,3 12,3 12,3M9.5,6A1.5,1.5 0 0,1 11,7.5A1.5,1.5 0 0,1 9.5,9A1.5,1.5 0 0,1 8,7.5A1.5,1.5 0 0,1 9.5,6M6.5,10A1.5,1.5 0 0,1 8,11.5A1.5,1.5 0 0,1 6.5,13A1.5,1.5 0 0,1 5,11.5A1.5,1.5 0 0,1 6.5,10M11.5,11A1.5,1.5 0 0,1 13,12.5A1.5,1.5 0 0,1 11.5,14A1.5,1.5 0 0,1 10,12.5A1.5,1.5 0 0,1 11.5,11M16.5,13A1.5,1.5 0 0,1 18,14.5A1.5,1.5 0 0,1 16.5,16H16.5A1.5,1.5 0 0,1 15,14.5H15A1.5,1.5 0 0,1 16.5,13M11,16A1.5,1.5 0 0,1 12.5,17.5A1.5,1.5 0 0,1 11,19A1.5,1.5 0 0,1 9.5,17.5A1.5,1.5 0 0,1 11,16Z" />
          </svg>
          Cookie管理
        </div>
        <div class="page-type-info">
          <svg id="pageTypeIcon" class="page-type-icon" viewBox="0 0 24 24" width="18" height="18">
            <path fill="currentColor" d="M17,18C15.89,18 15,18.89 15,20A2,2 0 0,0 17,22A2,2 0 0,0 19,20C19,18.89 18.1,18 17,18M1,2V4H3L6.6,11.59L5.24,14.04C5.09,14.32 5,14.65 5,15A2,2 0 0,0 7,17H19V15H7.42A0.25,0.25 0 0,1 7.17,14.75C7.17,14.7 7.18,14.66 7.2,14.63L8.1,13H15.55C16.3,13 16.96,12.58 17.3,11.97L20.88,5.5C20.95,5.34 21,5.17 21,5A1,1 0 0,0 20,4H5.21L4.27,2M7,18C5.89,18 5,18.89 5,20A2,2 0 0,0 7,22A2,2 0 0,0 9,20C9,18.89 8.1,18 7,18Z" />
          </svg>
          <span id="pageTypeDisplay">加载中...</span>
          <span id="pageTypeBadge" class="badge badge-primary">页面类型</span>
        </div>
      </div>
      
      <div class="cookie-stats">
        <div class="stat-item">
          <div id="cookieCount" class="stat-value">0</div>
          <div class="stat-label">Cookie数量</div>
        </div>
        <div class="stat-item">
          <div id="cookieSize" class="stat-value">0</div>
          <div class="stat-label">总大小 (KB)</div>
        </div>
      </div>
      
      <div class="cookie-tools">
        <div class="tool-action" id="refreshCookies">
          <svg class="tool-icon" viewBox="0 0 24 24">
            <path fill="currentColor" d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z" />
          </svg>
          刷新
        </div>
        <div class="tool-action" id="addCookie">
          <svg class="tool-icon" viewBox="0 0 24 24">
            <path fill="currentColor" d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z" />
          </svg>
          添加
        </div>
        <div class="tool-action" id="filterCookies">
          <svg class="tool-icon" viewBox="0 0 24 24">
            <path fill="currentColor" d="M14,12V19.88C14.04,20.18 13.94,20.5 13.71,20.71C13.32,21.1 12.69,21.1 12.3,20.71L10.29,18.7C10.06,18.47 9.96,18.16 10,17.87V12H9.97L4.21,4.62C3.87,4.19 3.95,3.56 4.38,3.22C4.57,3.08 4.78,3 5,3H19C19.22,3 19.43,3.08 19.62,3.22C20.05,3.56 20.13,4.19 19.79,4.62L14.03,12H14Z" />
          </svg>
          过滤
        </div>
      </div>
      
      <!-- 显示模式选项卡 -->
      <div class="mode-tabs">
        <div class="mode-tab active" data-mode="list">列表视图</div>
        <div class="mode-tab" data-mode="table">表格视图</div>
        <div class="mode-tab" data-mode="raw">原始视图</div>
        <div class="mode-tab" data-mode="json">JSON视图</div>
      </div>
      
      <div class="cookie-container">
        <input type="text" class="cookie-search" id="cookieSearch" placeholder="搜索Cookie (名称或值)">
        
        <!-- 列表视图 -->
        <div class="cookie-content-wrapper">
          <div id="cookieContent">正在获取Cookie...</div>
        </div>
        
        <!-- 表格视图 -->
        <div id="tableView" class="table-view">
          <div class="table-header">
            <div class="cookie-cell">名称</div>
            <div class="cookie-cell">值</div>
            <div class="cookie-cell">操作</div>
          </div>
          <div id="tableBody" class="table-body">
            <!-- 表格内容将通过JS动态生成 -->
          </div>
        </div>
        
        <!-- JSON视图 -->
        <div id="jsonView" class="json-view">
          <!-- JSON内容将通过JS动态生成 -->
        </div>
      </div>
    </div>
    
    <!-- Action Button -->
    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 16px;">
      <button id="copyCookie" class="action-button">
        <svg class="button-icon" viewBox="0 0 24 24">
          <path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z" />
        </svg>
        复制所有Cookie
      </button>
      <div id="feishuParamActions" style="display:none;"></div>
    </div>
    
    <!-- Status Message -->
    <div id="status"></div>
    
    <!-- Cookie编辑模态框 -->
    <div id="cookieModal" class="modal-overlay">
      <div class="modal">
        <div class="modal-header">
          <h3 id="modalTitle" class="modal-title">编辑Cookie</h3>
          <button id="closeModal" class="modal-close">
            <svg width="18" height="18" viewBox="0 0 24 24">
              <path fill="currentColor" d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
            </svg>
          </button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label for="cookieName" class="form-label">名称</label>
            <input type="text" id="cookieName" class="form-input" placeholder="Cookie名称">
          </div>
          <div class="form-group">
            <label for="cookieValue" class="form-label">值</label>
            <textarea id="cookieValue" class="form-input form-textarea" placeholder="Cookie值"></textarea>
          </div>
          <div class="form-group">
            <label for="cookieDomain" class="form-label">域</label>
            <input type="text" id="cookieDomain" class="form-input" placeholder="域名 (例如: example.com)">
          </div>
          <div class="form-group">
            <label for="cookiePath" class="form-label">路径</label>
            <input type="text" id="cookiePath" class="form-input" placeholder="路径 (例如: /)">
          </div>
        </div>
        <div class="modal-footer">
          <button id="cancelCookie" class="modal-button button-secondary">取消</button>
          <button id="saveCookie" class="modal-button button-primary">保存</button>
        </div>
      </div>
    </div>
    
    <!-- QR Code Section -->
    <div class="qrcode-section">
      <h3 class="qrcode-title">
        <svg class="section-title-icon" viewBox="0 0 24 24">
          <path fill="currentColor" d="M4,4H10V10H4V4M20,4V10H14V4H20M14,15H16V13H14V11H16V13H18V11H20V13H18V15H20V18H18V20H16V18H13V20H11V16H14V15M16,15V18H18V15H16M4,20V14H10V20H4M6,6V8H8V6H6M16,6V8H18V6H16M6,16V18H8V16H6M4,11H6V13H4V11M9,11H13V15H11V13H9V11M11,6H13V10H11V6M2,2V6H0V2A2,2 0 0,1 2,0H6V2H2M22,0A2,2 0 0,1 24,2V6H22V2H18V0H22M2,18V22H6V24H2A2,2 0 0,1 0,22V18H2M22,22V18H24V22A2,2 0 0,1 22,24H18V22H22Z" />
        </svg>
        扫码进AI交流群
      </h3>
      <img src="icons/weixin.png" class="qrcode-img" alt="微信二维码">
      <p class="qrcode-description">
        添加微信 <span class="highlight">cyanlis8</span> 立即加入AI爱好者社区
      </p>
    </div>
    
    <!-- Footer -->
    <div class="footer">
      © 2025 Cookie复制工具 v2.2 | 开发者：姚安
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>