document.addEventListener('DOMContentLoaded', function() {
  // 获取DOM元素
  const copyCookieBtn = document.getElementById('copyCookie');
  const refreshCookiesBtn = document.getElementById('refreshCookies');
  const addCookieBtn = document.getElementById('addCookie');
  const filterCookiesBtn = document.getElementById('filterCookies');
  const cookieSearch = document.getElementById('cookieSearch');
  const cookieContent = document.getElementById('cookieContent');
  const tableView = document.getElementById('tableView');
  const tableBody = document.getElementById('tableBody');
  const jsonView = document.getElementById('jsonView');
  const status = document.getElementById('status');
  const cookieCount = document.getElementById('cookieCount');
  const cookieSize = document.getElementById('cookieSize');
  const themeToggle = document.getElementById('themeToggle');
  const pageTypeDisplay = document.getElementById('pageTypeDisplay');
  const pageTypeIcon = document.getElementById('pageTypeIcon');
  const pageTypeBadge = document.getElementById('pageTypeBadge');
  const feishuParamActions = document.getElementById('feishuParamActions');
  
  // 模态框元素
  const cookieModal = document.getElementById('cookieModal');
  const modalTitle = document.getElementById('modalTitle');
  const cookieName = document.getElementById('cookieName');
  const cookieValue = document.getElementById('cookieValue');
  const cookieDomain = document.getElementById('cookieDomain');
  const cookiePath = document.getElementById('cookiePath');
  const closeModal = document.getElementById('closeModal');
  const cancelCookie = document.getElementById('cancelCookie');
  const saveCookie = document.getElementById('saveCookie');
  
  // 模式选项卡
  const modeTabs = document.querySelectorAll('.mode-tab');
  
  // 存储获取到的cookies
  let currentCookies = [];
  let displayMode = 'list'; // 'list', 'table', 'raw', 'json'
  let editingCookie = null; // 当前正在编辑的cookie
  let currentPageType = ''; // 当前页面类型
  
  // 初始化
  initialize();
  
  // 初始化函数
  async function initialize() {
    try {
      // 检查关键DOM元素
      if (!cookieContent) {
        console.error('Cookie内容元素不存在');
      }
      
      // 应用保存的主题设置
      applyThemePreference();
      
      // 获取当前cookies
      await refreshCookies();
      
      // 设置事件监听器
      setupEventListeners();
    } catch (error) {
      console.error('初始化出错:', error);
      if (status) {
        showError('插件初始化失败，请刷新重试');
      }
    }
  }
  
  // 设置事件监听器
  function setupEventListeners() {
    try {
      // 复制按钮点击
      if (copyCookieBtn) {
        copyCookieBtn.addEventListener('click', copyAllCookies);
      }
      
      // 刷新按钮点击
      if (refreshCookiesBtn) {
        refreshCookiesBtn.addEventListener('click', refreshCookies);
      }
      
      // 添加按钮点击
      if (addCookieBtn) {
        addCookieBtn.addEventListener('click', showAddCookieModal);
      }
      
      // 过滤按钮点击（打开/关闭过滤框）
      if (filterCookiesBtn && cookieSearch) {
        filterCookiesBtn.addEventListener('click', function() {
          cookieSearch.focus();
        });
      }
      
      // 搜索框输入
      if (cookieSearch) {
        cookieSearch.addEventListener('input', function() {
          renderCookies(currentCookies, this.value);
        });
      }
      
      // 模式选项卡切换
      if (modeTabs && modeTabs.length > 0) {
        modeTabs.forEach(tab => {
          if (tab) {
            tab.addEventListener('click', function() {
              const mode = this.getAttribute('data-mode');
              switchDisplayMode(mode);
            });
          }
        });
      }
      
      // 模态框关闭按钮
      if (closeModal) {
        closeModal.addEventListener('click', hideModal);
      }
      
      if (cancelCookie) {
        cancelCookie.addEventListener('click', hideModal);
      }
      
      // 保存Cookie按钮
      if (saveCookie) {
        saveCookie.addEventListener('click', saveEditedCookie);
      }
      
      // 主题切换
      if (themeToggle) {
        themeToggle.addEventListener('click', toggleTheme);
      }
    } catch (error) {
      console.error('设置事件监听器出错:', error);
    }
  }
  
  // 主题切换
  function toggleTheme() {
    try {
      if (!document.body || !themeToggle) return;
      
      const isDarkMode = document.body.classList.toggle('dark-mode');
      
      // 更新图标
      if (isDarkMode) {
        themeToggle.innerHTML = `
          <svg class="theme-icon" viewBox="0 0 24 24">
            <path fill="currentColor" d="M12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9Z" />
          </svg>
        `;
      } else {
        themeToggle.innerHTML = `
          <svg class="theme-icon" viewBox="0 0 24 24">
            <path fill="currentColor" d="M7.5,2C5.71,3.15 4.5,5.18 4.5,7.5C4.5,9.82 5.71,11.85 7.5,13C4.91,13 2.5,10.59 2.5,8C2.5,5.41 4.91,3 7.5,3M19.07,3.5C20.21,4.66 20.5,6.03 20.5,7.5C20.5,12.28 16.72,16 12,16C11.5,16 11.03,15.97 10.56,15.91C10.73,15.28 10.84,14.63 10.91,13.97C11.03,14.42 11.5,14.8 12,14.8C13.81,14.8 15.5,13.42 15.5,12C15.5,10.58 13.81,9.2 12,9.2C11.43,9.2 10.88,9.35 10.39,9.61C9.88,8.93 9.59,8.08 9.54,7.16C8.88,7.05 8.21,7 7.5,7C7.35,7 7.21,7.01 7.06,7.02C7.1,6.86 7.13,6.69 7.13,6.5C7.13,4.47 5.63,2.74 3.68,2.35C4.17,2.14 4.59,2 5,2C5.92,2 6.74,2.5 7.5,2M19.07,17C19.68,17 20.25,16.73 20.66,16.32C20.26,16.69 19.71,16.89 19.07,17M12,22C14.05,20.89 15.71,19.08 16.5,17C13.93,15.95 12,13.81 12,11.5C12,9.19 13.93,7.05 16.5,6C15.54,3.5 13.5,2 12,2C16.42,2 20,5.58 20,10C20,14.42 16.42,18 12,18C10.39,18 8.9,17.5 7.74,16.61C8.73,18.95 10.28,20.85 12,22Z" />
          </svg>
        `;
      }
      
      // 保存主题偏好
      try {
        localStorage.setItem('darkMode', isDarkMode);
      } catch (e) {
        console.error('无法保存主题设置:', e);
      }
    } catch (error) {
      console.error('切换主题出错:', error);
    }
  }
  
  // 应用主题偏好
  function applyThemePreference() {
    try {
      if (!document.body || !themeToggle) return;
      
      let isDarkMode = false;
      try {
        isDarkMode = localStorage.getItem('darkMode') === 'true';
      } catch (e) {
        console.error('无法读取主题设置:', e);
      }
      
      if (isDarkMode) {
        document.body.classList.add('dark-mode');
        themeToggle.innerHTML = `
          <svg class="theme-icon" viewBox="0 0 24 24">
            <path fill="currentColor" d="M12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9Z" />
          </svg>
        `;
      }
    } catch (error) {
      console.error('应用主题设置出错:', error);
    }
  }
  
  // 切换显示模式
  function switchDisplayMode(mode) {
    try {
      displayMode = mode;
      
      // 更新活动选项卡
      if (modeTabs && modeTabs.length > 0) {
        modeTabs.forEach(tab => {
          if (tab) {
            if (tab.getAttribute('data-mode') === mode) {
              tab.classList.add('active');
            } else {
              tab.classList.remove('active');
            }
          }
        });
      }
      
      // 隐藏所有视图
      if (cookieContent && cookieContent.parentElement) {
        cookieContent.parentElement.style.display = 'none';
      }
      
      if (tableView) {
        tableView.style.display = 'none';
      }
      
      if (jsonView) {
        jsonView.style.display = 'none';
      }
      
      // 显示选定的视图
      switch (mode) {
        case 'list':
          if (cookieContent && cookieContent.parentElement) {
            cookieContent.parentElement.style.display = 'block';
          }
          break;
        case 'table':
          if (tableView) {
            tableView.style.display = 'block';
          }
          break;
        case 'raw':
          if (cookieContent && cookieContent.parentElement) {
            cookieContent.parentElement.style.display = 'block';
          }
          break;
        case 'json':
          if (jsonView) {
            jsonView.style.display = 'block';
          }
          break;
      }
      
      // 重新渲染数据
      renderCookies(currentCookies, cookieSearch ? cookieSearch.value : '');
    } catch (error) {
      console.error('切换显示模式出错:', error);
    }
  }
  
  // 识别小红书页面类型
  function identifyXiaohongshuPageType(url) {
    if (!url) return '未知页面';
    
    try {
      const urlObj = new URL(url);
      const urlPath = urlObj.pathname;
      const urlParams = urlObj.search;
      
      // 小红书笔记详情页: 包含/explore/后跟ID，且通常有xsec_token参数
      if (urlPath.includes('/explore/') && /\/explore\/[a-zA-Z0-9]+/.test(urlPath)) {
        return '小红书笔记详情页';
      }
      
      // 小红书发现页: 包含/explore但不包含后续ID
      if (urlPath === '/explore' || urlPath === '/explore/') {
        // 获取channel_id参数，用于识别具体的发现页类型
        const channelId = new URLSearchParams(urlParams).get('channel_id');
        
        if (channelId) {
          // 根据channel_id识别具体的发现页类型
          switch (channelId) {
            case 'homefeed_recommend':
              return '小红书发现页-推荐';
            case 'homefeed.fashion_v3':
              return '小红书发现页-穿搭';
            case 'homefeed.food_v3':
              return '小红书发现页-美食';
            case 'homefeed.cosmetics_v3':
              return '小红书发现页-彩妆';
            case 'homefeed.movie_and_tv_v3':
              return '小红书发现页-影视';
            case 'homefeed.career_v3':
              return '小红书发现页-职场';
            case 'homefeed.love_v3':
              return '小红书发现页-情感';
            case 'homefeed.household_product_v3':
              return '小红书发现页-家居';
            case 'homefeed.gaming_v3':
              return '小红书发现页-游戏';
            case 'homefeed.travel_v3':
              return '小红书发现页-旅行';
            case 'homefeed.fitness_v3':
              return '小红书发现页-健身';
            default:
              // 如果是其他homefeed开头的channel_id，也识别为发现页
              if (channelId.startsWith('homefeed')) {
                return '小红书发现页-其他';
              }
              return '小红书发现页';
          }
        }
        
        return '小红书发现页';
      }
      
      // 小红书搜索页: 包含search_result和keyword参数
      if (urlPath.includes('/search_result') && urlParams.includes('keyword')) {
        return '小红书搜索页';
      }
      
      // 小红书博主主页: 包含/user/profile/
      if (urlPath.includes('/user/profile/')) {
        return '小红书博主主页';
      }
      
      // 小红书通知页: 包含/notification
      if (urlPath.includes('/notification')) {
        return '小红书通知页';
      }
      
      // 其他小红书页面
      if (urlObj.hostname.includes('xiaohongshu.com')) {
        return '其他小红书页面';
      }
      
      return '未知网站页面';
    } catch (e) {
      console.error('解析URL失败:', e);
      return '未知页面';
    }
  }
  
  // 识别B站页面类型
  function identifyBilibiliPageType(url) {
    if (!url) return '未知页面';
    
    try {
      const urlObj = new URL(url);
      const urlPath = urlObj.pathname;
      const urlParams = urlObj.search;
      const hostname = urlObj.hostname;
      const searchParams = new URLSearchParams(urlParams);
      
      // 检查是否是B站域名
      if (!hostname.includes('bilibili.com')) {
        return '未知网站页面';
      }
      
      // B站主页
      if (hostname === 'www.bilibili.com' && (urlPath === '/' || urlPath === '')) {
        return 'B站主页';
      }
      
      // B站视频详情页
      if (urlPath.startsWith('/video/')) {
        return 'B站视频详情页';
      }
      
      // B站搜索页面
      if (hostname === 'search.bilibili.com') {
        // 搜索综合区
        if (urlPath.startsWith('/all')) {
          const order = searchParams.get('order');
          
          if (!order) {
            return 'B站搜索综合区-综合排序';
          }
          
          switch (order) {
            case 'click':
              return 'B站搜索综合区-最多播放';
            case 'pubdate':
              return 'B站搜索综合区-最新发布';
            case 'dm':
              return 'B站搜索综合区-最多弹幕';
            case 'stow':
              return 'B站搜索综合区-最多收藏';
            default:
              return 'B站搜索综合区-其他排序';
          }
        }
        
        // 搜索视频区
        if (urlPath.startsWith('/video')) {
          const order = searchParams.get('order');
          
          if (!order) {
            return 'B站搜索视频区-综合排序';
          }
          
          switch (order) {
            case 'click':
              return 'B站搜索视频区-最多播放';
            case 'pubdate':
              return 'B站搜索视频区-最新发布';
            case 'dm':
              return 'B站搜索视频区-最多弹幕';
            case 'stow':
              return 'B站搜索视频区-最多收藏';
            default:
              return 'B站搜索视频区-其他排序';
          }
        }
        
        // 搜索专栏区
        if (urlPath.startsWith('/article')) {
          const order = searchParams.get('order');
          
          if (!order) {
            return 'B站搜索专栏区-综合排序';
          }
          
          switch (order) {
            case 'pubdate':
              return 'B站搜索专栏区-最新发布';
            case 'click':
              return 'B站搜索专栏区-最多点击';
            case 'attention':
              return 'B站搜索专栏区-最多喜欢';
            case 'scores':
              return 'B站搜索专栏区-最多评论';
            default:
              return 'B站搜索专栏区-其他排序';
          }
        }
        
        // 搜索用户区
        if (urlPath.startsWith('/upuser')) {
          const order = searchParams.get('order');
          const orderSort = searchParams.get('order_sort');
          
          if (!order) {
            return 'B站搜索用户区-默认排序';
          }
          
          if (order === 'fans') {
            return orderSort === '1' ? 'B站搜索用户区-粉丝数由低到高' : 'B站搜索用户区-粉丝数由高到低';
          }
          
          if (order === 'level') {
            return orderSort === '1' ? 'B站搜索用户区-Lv等级由低到高' : 'B站搜索用户区-Lv等级由高到低';
          }
          
          return 'B站搜索用户区-其他排序';
        }
        
        return 'B站搜索页面';
      }
      
      // 其他B站页面
      return '其他B站页面';
      
    } catch (e) {
      console.error('解析URL失败:', e);
      return '未知页面';
    }
  }
  
  // 识别抖音页面类型
  function identifyDouyinPageType(url) {
    if (!url) return '未知页面';
    
    try {
      const urlObj = new URL(url);
      const urlPath = urlObj.pathname;
      const urlParams = urlObj.search;
      const hostname = urlObj.hostname;
      
      // 检查是否是抖音域名
      if (!hostname.includes('douyin.com')) {
        return '未知网站页面';
      }
      
      // 抖音直播页面
      if (hostname === 'live.douyin.com') {
        // 直播分类页
        if (urlPath.includes('/category/')) {
          const categoryMatch = urlPath.match(/\/category\/([\d_]+)/);
          if (categoryMatch && categoryMatch[1]) {
            const categoryId = categoryMatch[1];
            switch (categoryId) {
              case '1_1': return '抖音直播-射击游戏';
              case '1_2': return '抖音直播-竞技游戏';
              case '1_3': return '抖音直播-单机游戏';
              case '1_4': return '抖音直播-棋牌游戏';
              case '1_5': return '抖音直播-休闲益智';
              case '1_6': return '抖音直播-角色扮演';
              case '1_7': return '抖音直播-策略卡牌';
              case '3_10000': return '抖音直播-娱乐天地';
              case '3_10001': return '抖音直播-科技文化';
              default: return '抖音直播-其他分类';
            }
          }
        }
        return '抖音直播主页';
      }
      
      // 短链接视频页
      if (hostname === 'v.douyin.com') {
        return '抖音视频详情页';
      }
      
      // 以下是www.douyin.com域名下的页面
      
      // 抖音视频详情页
      if (urlPath.startsWith('/video/')) {
        return '抖音视频详情页';
      }
      
      // 模态框形式的视频详情页
      if (urlParams.includes('modal_id=')) {
        return '抖音视频详情页';
      }
      
      // 抖音搜索页
      if (urlPath.includes('/root/search/')) {
        const searchParams = new URLSearchParams(urlParams);
        const searchType = searchParams.get('type');
        
        if (searchType) {
          switch (searchType) {
            case 'general': return '抖音搜索页-综合';
            case 'video': return '抖音搜索页-视频';
            case 'user': return '抖音搜索页-用户';
            case 'live': return '抖音搜索页-直播';
            default: return '抖音搜索页-其他';
          }
        }
        return '抖音搜索页';
      }
      
      // 抖音博主主页
      if (urlPath.startsWith('/user/')) {
        return '抖音博主主页';
      }
      
      // 抖音精选页
      if (urlPath.startsWith('/discover')) {
        // 精选子分类
        const subCategory = urlPath.replace('/discover/', '');
        if (subCategory && subCategory !== '') {
          switch (subCategory) {
            case 'knowledge': return '抖音精选-知识';
            case 'sports': return '抖音精选-体育';
            case 'car': return '抖音精选-汽车';
            case 'acg': return '抖音精选-二次元';
            case 'game': return '抖音精选-游戏';
            case 'film': return '抖音精选-影视';
            case 'vlog': return '抖音精选-生活vlog';
            case 'travel': return '抖音精选-旅行';
            case 'theater': return '抖音精选-小剧场';
            case 'food': return '抖音精选-美食';
            case 'agriculture': return '抖音精选-三农';
            case 'music': return '抖音精选-音乐';
            case 'animal': return '抖音精选-动物';
            case 'child': return '抖音精选-亲子';
            case 'beauty': return '抖音精选-美妆';
            default: return '抖音精选-其他';
          }
        }
        return '抖音精选-全部';
      }
      
      // 抖音放映厅
      if (urlPath === '/vs') {
        return '抖音放映厅';
      }
      
      // 抖音短剧
      if (urlPath === '/series') {
        return '抖音短剧';
      }
      
      // 抖音关注页
      if (urlPath === '/follow') {
        return '抖音关注页';
      }
      
      // 抖音朋友页
      if (urlPath === '/friend') {
        return '抖音朋友页';
      }
      
      // 抖音推荐页
      if (urlPath === '/' && urlParams.includes('recommend=1')) {
        return '抖音推荐页';
      }
      
      // 抖音主页
      if (urlPath === '/') {
        return '抖音主页';
      }
      
      // 其他抖音页面
      return '其他抖音页面';
      
    } catch (e) {
      console.error('解析URL失败:', e);
      return '未知页面';
    }
  }
  
  // 更新页面类型显示
  function updatePageTypeDisplay(pageType) {
    if (!pageTypeDisplay || !pageTypeIcon || !pageTypeBadge) return;
    
    currentPageType = pageType;
    
    // 设置页面类型文本
    pageTypeDisplay.textContent = pageType;
    
    // 设置图标和徽章颜色
    let iconPath = '';
    let badgeClass = '';
    
    // 判断是否是飞书多维表格页面
    const isFeishuTable = pageType.includes('飞书多维表格');
    const isCozeWorkflow = pageType === '扣子工作流';
    const isCozeBot = pageType === '扣子智能体';
    if (feishuParamActions) feishuParamActions.innerHTML = '';
    if (isFeishuTable || isCozeWorkflow || isCozeBot) {
      chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
        if (tabs[0] && tabs[0].url) {
          try {
            const url = new URL(tabs[0].url);
            let paramList = [];
            if (isFeishuTable) {
              const urlPath = url.pathname;
              const urlParams = new URLSearchParams(url.search);
              let appToken = '';
              if (urlPath.includes('/base/')) {
                appToken = urlPath.split('/base/')[1].split('?')[0];
              } else if (urlPath.includes('/wiki/')) {
                appToken = urlPath.split('/wiki/')[1].split('?')[0];
              }
              const tableId = urlParams.get('table');
              const viewId = urlParams.get('view');
              paramList = [
                {label: 'app_token', value: appToken},
                {label: 'table_id', value: tableId},
                {label: 'view', value: viewId}
              ];
            } else if (isCozeWorkflow) {
              const urlParams = new URLSearchParams(url.search);
              const spaceId = urlParams.get('space_id');
              const workflowId = urlParams.get('workflow_id');
              paramList = [
                {label: 'space_id', value: spaceId},
                {label: 'workflow_id', value: workflowId}
              ];
            } else if (isCozeBot) {
              // 扣子智能体，提取bot_ID
              const urlPath = url.pathname;
              let botId = '';
              const match = urlPath.match(/\/bot\/(\d+)/);
              if (match && match[1]) {
                botId = match[1];
              }
              paramList = [
                {label: 'bot_ID', value: botId}
              ];
            }
            renderParamsArea(paramList);
            // 构建主操作区小按钮
            paramList.forEach(param => {
              if (!param.value) return;
              const btn = document.createElement('button');
              btn.className = 'feishu-param-btn';
              btn.title = `复制 ${param.label}`;
              btn.innerHTML = `<svg class=\"copy-icon\" viewBox=\"0 0 24 24\"><path fill=\"currentColor\" d=\"M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z\"/></svg> <span>${param.label}</span>`;
              btn.onclick = async () => {
                await copyToClipboard(param.value);
                showSuccess(`已复制: ${param.value}`);
              };
              feishuParamActions.appendChild(btn);
            });
            feishuParamActions.style.display = 'flex';
          } catch (e) {
            if (feishuParamActions) feishuParamActions.style.display = 'none';
            renderParamsArea([]);
          }
        }
      });
    } else {
      if (feishuParamActions) feishuParamActions.style.display = 'none';
      renderParamsArea([]);
    }
    
    // 判断是否是抖音页面
    const isDouyinPage = pageType.startsWith('抖音');
    
    if (isDouyinPage) {
      // 抖音页面使用特殊的徽章颜色
      badgeClass = 'badge-accent';
      
      // 根据抖音页面类型设置不同图标
      if (pageType.includes('视频详情')) {
        iconPath = 'M18,4L20,8H17L15,4H13L15,8H12L10,4H8L10,8H7L5,4H4A2,2 0 0,0 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V4H18Z';
      } else if (pageType.includes('直播')) {
        iconPath = 'M17,10.5V7A1,1 0 0,0 16,6H4A1,1 0 0,0 3,7V17A1,1 0 0,0 4,18H16A1,1 0 0,0 17,17V13.5L21,17.5V6.5L17,10.5Z';
      } else if (pageType.includes('搜索')) {
        iconPath = 'M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z';
      } else if (pageType.includes('博主')) {
        iconPath = 'M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z';
      } else if (pageType.includes('精选')) {
        iconPath = 'M12,15.39L8.24,17.66L9.23,13.38L5.91,10.5L10.29,10.13L12,6.09L13.71,10.13L18.09,10.5L14.77,13.38L15.76,17.66M22,9.24L14.81,8.63L12,2L9.19,8.63L2,9.24L7.45,13.97L5.82,21L12,17.27L18.18,21L16.54,13.97L22,9.24Z';
      } else if (pageType.includes('推荐')) {
        iconPath = 'M12,16L19.36,10.27L21,9L12,2L3,9L4.63,10.27M12,18.54L4.62,12.81L3,14.07L12,21.07L21,14.07L19.37,12.8L12,18.54Z';
      } else if (pageType.includes('关注')) {
        iconPath = 'M16,12A2,2 0 0,1 18,10A2,2 0 0,1 20,12A2,2 0 0,1 18,14A2,2 0 0,1 16,12M10,12A2,2 0 0,1 12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12M4,12A2,2 0 0,1 6,10A2,2 0 0,1 8,12A2,2 0 0,1 6,14A2,2 0 0,1 4,12Z';
      } else {
        iconPath = 'M10,15L15.19,12L10,9V15M21.56,7.17C21.69,7.64 21.78,8.27 21.84,9.07C21.91,9.87 21.94,10.56 21.94,11.16L22,12C22,14.19 21.84,15.8 21.56,16.83C21.31,17.73 20.73,18.31 19.83,18.56C19.36,18.69 18.5,18.78 17.18,18.84C15.88,18.91 14.69,18.94 13.59,18.94L12,19C7.81,19 5.2,18.84 4.17,18.56C3.27,18.31 2.69,17.73 2.44,16.83C2.31,16.36 2.22,15.73 2.16,14.93C2.09,14.13 2.06,13.44 2.06,12.84L2,12C2,9.81 2.16,8.2 2.44,7.17C2.69,6.27 3.27,5.69 4.17,5.44C4.64,5.31 5.5,5.22 6.82,5.16C8.12,5.09 9.31,5.06 10.41,5.06L12,5C16.19,5 18.8,5.16 19.83,5.44C20.73,5.69 21.31,6.27 21.56,7.17Z';
      }
    } else if (pageType.startsWith('B站')) {
      // B站页面使用特殊的徽章颜色
      badgeClass = 'badge-info';
      
      // 根据B站页面类型设置不同图标
      if (pageType.includes('视频详情')) {
        iconPath = 'M4,5V11H21V5M4,18H21V12H4M9,8H6V6H9M9,17H6V15H9';
      } else if (pageType.includes('搜索')) {
        iconPath = 'M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z';
      } else if (pageType.includes('主页')) {
        iconPath = 'M10,20V14H14V20H19V12H22L12,3L2,12H5V20H10Z';
      } else {
        iconPath = 'M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M12,9A3,3 0 0,1 15,12A3,3 0 0,1 12,15A3,3 0 0,1 9,12A3,3 0 0,1 12,9M12,11A1,1 0 0,0 11,12A1,1 0 0,0 12,13A1,1 0 0,0 13,12A1,1 0 0,0 12,11Z';
      }
    } else if (pageType === '未知网站页面' || pageType === '未知页面' || pageType === '其他网站页面') {
      // 未知网站页面使用特殊的徽章颜色和图标
      iconPath = 'M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,13H13V17H11V13Z';
      badgeClass = 'badge-secondary';
    } else if (pageType.includes('国资委') || pageType.includes('省国资委') || pageType.includes('市国资委')) {
      // 国资委网站页面
      badgeClass = 'badge-success';
      
      if (pageType.includes('首页')) {
        iconPath = 'M10,20V14H14V20H19V12H22L12,3L2,12H5V20H10Z';
      } else if (pageType.includes('招聘')) {
        iconPath = 'M20,6C20.58,6 21.05,6.2 21.42,6.59C21.8,7 22,7.45 22,8V19C22,19.55 21.8,20 21.42,20.41C21.05,20.8 20.58,21 20,21H4C3.42,21 2.95,20.8 2.58,20.41C2.2,20 2,19.55 2,19V8C2,7.45 2.2,7 2.58,6.59C2.95,6.2 3.42,6 4,6H8V4C8,3.42 8.2,2.95 8.58,2.58C8.95,2.2 9.42,2 10,2H14C14.58,2 15.05,2.2 15.42,2.58C15.8,2.95 16,3.42 16,4V6H20M4,8V19H20V8H4M14,6V4H10V6H14Z';
      } else if (pageType.includes('文章')) {
        iconPath = 'M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,5V19H5V5H19Z';
      } else {
        iconPath = 'M17.9,17.39C17.64,16.59 16.89,16 16,16H15V13A1,1 0 0,0 14,12H8V10H10A1,1 0 0,0 11,9V7H13A2,2 0 0,0 15,5V4.59C17.93,5.77 20,8.64 20,12C20,14.08 19.2,15.97 17.9,17.39M11,19.93C7.05,19.44 4,16.08 4,12C4,11.38 4.08,10.78 4.21,10.21L9,15V16A2,2 0 0,0 11,18M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z';
      }
    } else if (pageType.includes('高校') || pageType.includes('就业信息网')) {
      // 高校就业网站页面
      badgeClass = 'badge-warning';
      
      if (pageType.includes('首页')) {
        iconPath = 'M10,20V14H14V20H19V12H22L12,3L2,12H5V20H10Z';
      } else if (pageType.includes('招聘')) {
        iconPath = 'M20,6C20.58,6 21.05,6.2 21.42,6.59C21.8,7 22,7.45 22,8V19C22,19.55 21.8,20 21.42,20.41C21.05,20.8 20.58,21 20,21H4C3.42,21 2.95,20.8 2.58,20.41C2.2,20 2,19.55 2,19V8C2,7.45 2.2,7 2.58,6.59C2.95,6.2 3.42,6 4,6H8V4C8,3.42 8.2,2.95 8.58,2.58C8.95,2.2 9.42,2 10,2H14C14.58,2 15.05,2.2 15.42,2.58C15.8,2.95 16,3.42 16,4V6H20M4,8V19H20V8H4M14,6V4H10V6H14Z';
      } else if (pageType.includes('文章')) {
        iconPath = 'M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,5V19H5V5H19Z';
      } else {
        iconPath = 'M12,3L1,9L12,15L21,10.09V17H23V9M5,13.18V17.18L12,21L19,17.18V13.18L12,17L5,13.18Z';
      }
    } else if (pageType.includes('微信公众号')) {
      // 微信公众号页面
      badgeClass = 'badge-success';
      iconPath = 'M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z';
    } else if (pageType.includes('飞书')) {
      // 飞书页面
      badgeClass = 'badge-info';
      
      if (pageType.includes('云文档')) {
        iconPath = 'M13,9H18.5L13,3.5V9M6,2H14L20,8V20A2,2 0 0,1 18,22H6C4.89,22 4,21.1 4,20V4C4,2.89 4.89,2 6,2M15,18V16H6V18H15M18,14V12H6V14H18Z';
      } else if (pageType.includes('多维表格')) {
        iconPath = 'M10,4V8H14V4H10M16,4V8H20V4H16M16,10V14H20V10H16M16,16V20H20V16H16M14,20V16H10V20H14M8,20V16H4V20H8M8,14V10H4V14H8M8,8V4H4V8H8M10,14H14V10H10V14M4,2H20A2,2 0 0,1 22,4V20A2,2 0 0,1 20,22H4C2.92,22 2,21.1 2,20V4A2,2 0 0,1 4,2Z';
      } else if (pageType.includes('表格')) {
        iconPath = 'M10,4V8H14V4H10M16,4V8H20V4H16M16,10V14H20V10H16M16,16V20H20V16H16M14,20V16H10V20H14M8,20V16H4V20H8M8,14V10H4V14H8M8,8V4H4V8H8M10,14H14V10H10V14M4,2H20A2,2 0 0,1 22,4V20A2,2 0 0,1 20,22H4C2.92,22 2,21.1 2,20V4A2,2 0 0,1 4,2Z';
      } else {
        iconPath = 'M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,5V19H5V5H19Z';
      }
    } else if (pageType.includes('扣子')) {
      // 扣子平台页面
      badgeClass = 'badge-primary';
      
      if (pageType.includes('工作流')) {
        iconPath = 'M3,3H21V7H3V3M4,8H20V21H4V8M9.5,11A0.5,0.5 0 0,0 9,11.5V13H15V11.5A0.5,0.5 0 0,0 14.5,11H9.5Z';
      } else if (pageType.includes('智能体')) {
        iconPath = 'M12,2A2,2 0 0,1 14,4C14,4.74 13.6,5.39 13,5.73V7H14A7,7 0 0,1 21,14H22A1,1 0 0,1 23,15V18A1,1 0 0,1 22,19H21V20A2,2 0 0,1 19,22H5A2,2 0 0,1 3,20V19H2A1,1 0 0,1 1,18V15A1,1 0 0,1 2,14H3A7,7 0 0,1 10,7H11V5.73C10.4,5.39 10,4.74 10,4A2,2 0 0,1 12,2M7.5,13A2.5,2.5 0 0,0 5,15.5A2.5,2.5 0 0,0 7.5,18A2.5,2.5 0 0,0 10,15.5A2.5,2.5 0 0,0 7.5,13M16.5,13A2.5,2.5 0 0,0 14,15.5A2.5,2.5 0 0,0 16.5,18A2.5,2.5 0 0,0 19,15.5A2.5,2.5 0 0,0 16.5,13Z';
      } else if (pageType.includes('应用')) {
        iconPath = 'M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,5V19H5V5H19M17,17H7V7H17V17M15,15H9V9H15V15Z';
      } else if (pageType.includes('工作空间')) {
        if (pageType.includes('发布管理')) {
          if (pageType.includes('智能体发布')) {
            iconPath = 'M12,2A2,2 0 0,1 14,4C14,4.74 13.6,5.39 13,5.73V7H14A7,7 0 0,1 21,14H22A1,1 0 0,1 23,15V18A1,1 0 0,1 22,19H21V20A2,2 0 0,1 19,22H5A2,2 0 0,1 3,20V19H2A1,1 0 0,1 1,18V15A1,1 0 0,1 2,14H3A7,7 0 0,1 10,7H11V5.73C10.4,5.39 10,4.74 10,4A2,2 0 0,1 12,2M7.5,13A2.5,2.5 0 0,0 5,15.5A2.5,2.5 0 0,0 7.5,18A2.5,2.5 0 0,0 10,15.5A2.5,2.5 0 0,0 7.5,13M16.5,13A2.5,2.5 0 0,0 14,15.5A2.5,2.5 0 0,0 16.5,18A2.5,2.5 0 0,0 19,15.5A2.5,2.5 0 0,0 16.5,13Z';
          } else if (pageType.includes('应用发布')) {
            iconPath = 'M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,5V19H5V5H19M17,17H7V7H17V17M15,15H9V9H15V15Z';
          } else if (pageType.includes('工作流发布')) {
            iconPath = 'M3,3H21V7H3V3M4,8H20V21H4V8M9.5,11A0.5,0.5 0 0,0 9,11.5V13H15V11.5A0.5,0.5 0 0,0 14.5,11H9.5Z';
          } else {
            iconPath = 'M5,3H19A2,2 0 0,1 21,5V19A2,2 0 0,1 19,21H5A2,2 0 0,1 3,19V5A2,2 0 0,1 5,3M5,5V19H19V5H5M17,17H7V7H17V17M15,15H9V9H15V15M12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12A2,2 0 0,1 12,10Z';
          }
        } else if (pageType.includes('模型管理')) {
          iconPath = 'M12,2A2,2 0 0,1 14,4C14,4.74 13.6,5.39 13,5.73V7H14A7,7 0 0,1 21,14H22A1,1 0 0,1 23,15V18A1,1 0 0,1 22,19H21V20A2,2 0 0,1 19,22H5A2,2 0 0,1 3,20V19H2A1,1 0 0,1 1,18V15A1,1 0 0,1 2,14H3A7,7 0 0,1 10,7H11V5.73C10.4,5.39 10,4.74 10,4A2,2 0 0,1 12,2M7.5,13A2.5,2.5 0 0,0 5,15.5A2.5,2.5 0 0,0 7.5,18A2.5,2.5 0 0,0 10,15.5A2.5,2.5 0 0,0 7.5,13M16.5,13A2.5,2.5 0 0,0 14,15.5A2.5,2.5 0 0,0 16.5,18A2.5,2.5 0 0,0 19,15.5A2.5,2.5 0 0,0 16.5,13Z';
        } else if (pageType.includes('效果评估')) {
          iconPath = 'M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M9,17H7V10H9V17M13,17H11V7H13V17M17,17H15V13H17V17Z';
        } else if (pageType.includes('成员与设置')) {
          iconPath = 'M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z';
        } else if (pageType.includes('项目开发')) {
          iconPath = 'M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,5V19H5V5H19M17,17H7V7H17V17M15,15H9V9H15V15Z';
        } else if (pageType.includes('资源库')) {
          iconPath = 'M20,6H12L10,4H4A2,2 0 0,0 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V8A2,2 0 0,0 20,6M20,18H4V8H20V18Z';
        } else {
          iconPath = 'M12,3L2,12H5V20H19V12H22L12,3M12,8.5C14.34,8.5 16.46,9.43 18,10.94L16.8,12.12C15.58,10.91 13.88,10.17 12,10.17C10.12,10.17 8.42,10.91 7.2,12.12L6,10.94C7.54,9.43 9.66,8.5 12,8.5M12,11.83C13.4,11.83 14.67,12.39 15.6,13.3L14.4,14.47C13.79,13.87 12.94,13.5 12,13.5C11.06,13.5 10.21,13.87 9.6,14.47L8.4,13.3C9.33,12.39 10.6,11.83 12,11.83M12,15.17C12.94,15.17 13.7,15.91 13.7,16.83C13.7,17.75 12.94,18.5 12,18.5C11.06,18.5 10.3,17.75 10.3,16.83C10.3,15.91 11.06,15.17 12,15.17Z';
        }
      } else if (pageType.includes('主页')) {
        iconPath = 'M10,20V14H14V20H19V12H22L12,3L2,12H5V20H10Z';
      } else {
        iconPath = 'M17.9,17.39C17.64,16.59 16.89,16 16,16H15V13A1,1 0 0,0 14,12H8V10H10A1,1 0 0,0 11,9V7H13A2,2 0 0,0 15,5V4.59C17.93,5.77 20,8.64 20,12C20,14.08 19.2,15.97 17.9,17.39M11,19.93C7.05,19.44 4,16.08 4,12C4,11.38 4.08,10.78 4.21,10.21L9,15V16A2,2 0 0,0 11,18M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z';
      }
    
    } else {
      // 小红书页面的图标设置
      switch(pageType) {
        case '小红书笔记详情页':
          iconPath = 'M19,5V19H5V5H19M21,3H3V21H21V3M17,17H7V16H17V17M17,15H7V14H17V15M17,12H7V7H17V12Z';
          badgeClass = 'badge-accent';
          break;
        case '小红书发现页':
          iconPath = 'M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z';
          badgeClass = 'badge-primary';
          break;
        case '小红书搜索页':
          iconPath = 'M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z';
          badgeClass = 'badge-primary';
          break;
        case '小红书博主主页':
          iconPath = 'M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z';
          badgeClass = 'badge-primary';
          break;
        case '小红书通知页':
          iconPath = 'M21,19V20H3V19L5,17V11C5,7.9 7.03,5.17 10,4.29C10,4.19 10,4.1 10,4A2,2 0 0,1 12,2A2,2 0 0,1 14,4C14,4.1 14,4.19 14,4.29C16.97,5.17 19,7.9 19,11V17L21,19M14,21A2,2 0 0,1 12,23A2,2 0 0,1 10,21';
          badgeClass = 'badge-primary';
          break;
        case '其他小红书页面':
          iconPath = 'M17.9,17.39C17.64,16.59 16.89,16 16,16H15V13A1,1 0 0,0 14,12H8V10H10A1,1 0 0,0 11,9V7H13A2,2 0 0,0 15,5V4.59C17.93,5.77 20,8.64 20,12C20,14.08 19.2,15.97 17.9,17.39M11,19.93C7.05,19.44 4,16.08 4,12C4,11.38 4.08,10.78 4.21,10.21L9,15V16A2,2 0 0,0 11,18M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z';
          badgeClass = 'badge-primary';
          break;
        default:
          iconPath = 'M17,18C15.89,18 15,18.89 15,20A2,2 0 0,0 17,22A2,2 0 0,0 19,20C19,18.89 18.1,18 17,18M1,2V4H3L6.6,11.59L5.24,14.04C5.09,14.32 5,14.65 5,15A2,2 0 0,0 7,17H19V15H7.42A0.25,0.25 0 0,1 7.17,14.75C7.17,14.7 7.18,14.66 7.2,14.63L8.1,13H15.55C16.3,13 16.96,12.58 17.3,11.97L20.88,5.5C20.95,5.34 21,5.17 21,5A1,1 0 0,0 20,4H5.21L4.27,2M7,18C5.89,18 5,18.89 5,20A2,2 0 0,0 7,22A2,2 0 0,0 9,20C9,18.89 8.1,18 7,18Z';
          badgeClass = 'badge-primary';
      }
    }
    
    // 更新图标
    pageTypeIcon.innerHTML = `<path fill="currentColor" d="${iconPath}" />`;
    
    // 更新徽章样式
    pageTypeBadge.className = `badge ${badgeClass}`;
  }
  
  // 识别其他网站页面类型（国资委、高校就业网等）
  function identifyOtherWebsitePageType(url) {
    if (!url) return '未知页面';
    
    try {
      const urlObj = new URL(url);
      const hostname = urlObj.hostname;
      const urlPath = urlObj.pathname;
      const urlParams = urlObj.search;
      
      // 1. 国资委网站
      if (hostname === 'www.sasac.gov.cn') {
        // 官网首页
        if (urlPath === '/' || urlPath === '') {
          return '国资委官网首页';
        }
        // 招聘专栏
        if (urlPath.includes('/n2588035/n2588325/n2588350/index.html')) {
          return '国资委招聘专栏';
        }
        // 文章详情页
        if (urlPath.includes('/content.html')) {
          return '国资委文章详情页';
        }
        return '国资委其他页面';
      }
      
      // 2. 山东省国资委网站
      if (hostname === 'gzw.shandong.gov.cn') {
        // 官网首页
        if (urlPath === '/' || urlPath === '') {
          return '山东省国资委官网首页';
        }
        // 招聘专栏
        if (urlPath.includes('/channels/ch00223/')) {
          return '山东省国资委招聘专栏';
        }
        // 文章详情页
        if (urlPath.includes('/articles/ch00223/')) {
          return '山东省国资委文章详情页';
        }
        return '山东省国资委其他页面';
      }
      
      // 3. 山东高校毕业生就业网
      if (hostname === 'www.sdgxbys.cn') {
        // 官网首页
        if (urlPath === '/' || urlPath === '') {
          return '山东高校毕业生就业网首页';
        }
        // 国企招聘专栏
        if (urlPath.includes('/col/jysc/') && urlParams.includes('cn=gqzp')) {
          return '山东高校毕业生就业网国企招聘专栏';
        }
        // 文章详情页
        if (urlPath.includes('/art/gqzp/')) {
          return '山东高校毕业生就业网文章详情页';
        }
        return '山东高校毕业生就业网其他页面';
      }
      
      // 4. 济南市国资委网站
      if (hostname === 'jngzw.jinan.gov.cn') {
        // 官网首页
        if (urlPath === '/' || urlPath === '') {
          return '济南市国资委官网首页';
        }
        // 招聘专栏
        if (urlPath.includes('/col/col23870/index.html')) {
          return '济南市国资委招聘专栏';
        }
        // 文章详情页
        if (urlPath.includes('/art/') && urlPath.includes('_23870_')) {
          return '济南市国资委文章详情页';
        }
        return '济南市国资委其他页面';
      }
      
      // 5. 高校就业信息网
      // 5.1 山东大学就业信息网
      if (hostname === 'job.sdu.edu.cn') {
        return '山东大学就业信息网';
      }
      
      // 5.2 山东财经大学就业信息网
      if (hostname === 'jobsdufe.sdbys.com') {
        // 官网首页
        if (urlPath === '/' || urlPath === '') {
          return '山东财经大学就业信息网首页';
        }
        // 招聘专栏
        if (urlPath === '/campus') {
          return '山东财经大学就业信息网招聘专栏';
        }
        // 文章详情页
        if (urlPath.includes('/campus/view/id/')) {
          return '山东财经大学就业信息网文章详情页';
        }
        return '山东财经大学就业信息网其他页面';
      }
      
      // 6. 微信公众号文章
      if (hostname === 'mp.weixin.qq.com') {
        if (urlPath.startsWith('/s')) {
          return '微信公众号文章页';
        }
        return '微信公众号其他页面';
      }
      
      // 7. 飞书文档
      if (hostname.endsWith('.feishu.cn')) {
        // 云文档
        if (urlPath.includes('/wiki/')) {
          // 检查是否是知识库中的多维表格
          if (urlParams.includes('table=')) {
            return '飞书多维表格-知识库';
          }
          return '飞书云文档-知识库';
        }
        if (urlPath.includes('/docx/')) {
          return '飞书云文档-文档';
        }
        // 多维表格 - 确保能识别所有飞书多维表格页面
        if (urlPath.includes('/base/')) {
          return '飞书多维表格-普通';
        }
        // 表格
        if (urlPath.includes('/sheets/')) {
          return '飞书表格';
        }
        return '飞书其他页面';
      }
      
      // 检查是否是飞书域名的其他子域名
      if (hostname.includes('.feishu.cn') || hostname === 'feishu.cn') {
        if (urlPath.includes('/base/')) {
          return '飞书多维表格-普通';
        }
        return '飞书其他页面';
      }
      
      // 8. 扣子平台
      if (hostname === 'www.coze.cn') {
        // 扣子主页
        if (urlPath === '/home') {
          return '扣子主页';
        }
        // 扣子工作流
        if (urlPath === '/work_flow' && urlParams.includes('workflow_id=')) {
          return '扣子工作流';
        }
        // 扣子工作空间
        if (urlPath.startsWith('/space/')) {
          const spaceId = urlPath.split('/')[2];
          if (urlPath.includes('/bot/')) {
            return '扣子智能体';
          } else if (urlPath.includes('/project-ide/')) {
            return '扣子应用';
          } else if (urlPath.includes('/develop')) {
            return '扣子工作空间-项目开发';
          } else if (urlPath.includes('/library')) {
            return '扣子工作空间-资源库';
          } else if (urlPath.includes('/publish')) {
            // 根据tab参数识别不同类型的发布管理
            const searchParams = new URLSearchParams(urlParams);
            const tab = searchParams.get('tab');
            if (tab === 'agent') {
              return '扣子工作空间-智能体发布';
            } else if (tab === 'app') {
              return '扣子工作空间-应用发布';
            } else if (tab === 'workflow') {
              return '扣子工作空间-工作流发布';
            }
            return '扣子工作空间-发布管理';
          } else if (urlPath.includes('/model')) {
            return '扣子工作空间-模型管理';
          } else if (urlPath.includes('/evaluate')) {
            return '扣子工作空间-效果评估';
          } else if (urlPath.includes('/team')) {
            return '扣子工作空间-成员与设置';
          } else {
            return '扣子工作空间';
          }
        }
        return '扣子平台其他页面';
      }
      
      return '未知网站页面';
    } catch (e) {
      console.error('解析URL失败:', e);
      return '未知页面';
    }
  }
  
  // 刷新Cookies
  async function refreshCookies() {
    try {
      if (cookieContent) {
        showLoading();
      }
      
      // 获取当前活动标签页
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      if (!tab || !tab.url) {
        console.error('无法获取当前标签页信息');
        showError('无法获取当前页面信息');
        return [];
      }
      
      // 识别页面类型
      let pageType = identifyXiaohongshuPageType(tab.url);
      
      // 如果不是小红书页面，尝试识别抖音页面
      if (pageType === '未知网站页面') {
        pageType = identifyDouyinPageType(tab.url);
      }
      
      // 如果不是小红书或抖音页面，尝试识别B站页面
      if (pageType === '未知网站页面') {
        pageType = identifyBilibiliPageType(tab.url);
      }
      
      // 如果不是小红书、抖音或B站页面，尝试识别其他网站页面（国资委、高校就业网、微信公众号、飞书等）
      if (pageType === '未知网站页面') {
        pageType = identifyOtherWebsitePageType(tab.url);
      }
      
      // 此时pageType已经是最终结果，无需再次赋值
      
      updatePageTypeDisplay(pageType);
      
      // 获取所有cookies
      const cookies = await chrome.cookies.getAll({ url: tab.url });
      currentCookies = cookies || [];
      
      // 填充域名和路径
      try {
        const url = new URL(tab.url);
        if (cookieDomain) cookieDomain.value = url.hostname;
        if (cookiePath) cookiePath.value = '/';
      } catch (e) {
        console.error('解析URL失败:', e);
      }
      
      // 更新统计信息
      updateCookieStats(cookies);
      
      // 呈现cookie内容
      renderCookies(cookies, cookieSearch ? cookieSearch.value : '');
      
      return cookies;
    } catch (error) {
      console.error('获取Cookie失败:', error);
      showError('获取Cookie失败: ' + error.message);
      return [];
    }
  }
  
  // 显示加载状态
  function showLoading() {
    if (cookieContent) {
      cookieContent.innerHTML = '<div style="text-align:center;padding:20px;color:var(--text-light);">正在获取Cookie...</div>';
    }
  }
  
  // 更新Cookie统计信息
  function updateCookieStats(cookies) {
    if (!cookies) cookies = [];
    
    // 更新数量
    if (cookieCount) {
      cookieCount.textContent = cookies.length;
    }
    
    // 计算总大小
    let totalSize = 0;
    cookies.forEach(cookie => {
      totalSize += (cookie.name.length + cookie.value.length);
    });
    
    // 转换为KB并保留一位小数
    const sizeInKB = (totalSize / 1024).toFixed(1);
    if (cookieSize) {
      cookieSize.textContent = sizeInKB;
    }
  }
  
  // 渲染Cookie内容
  function renderCookies(cookies, searchTerm = '') {
    if (!cookies) cookies = [];
    
    const emptyMessage = '<div style="text-align:center;padding:20px;color:var(--text-light);">没有找到Cookie</div>';
    
    if (!cookies || cookies.length === 0) {
      if (cookieContent) cookieContent.innerHTML = emptyMessage;
      if (tableBody) tableBody.innerHTML = emptyMessage;
      if (jsonView) jsonView.textContent = '{\n  "cookies": []\n}';
      return;
    }
    
    // 如果有搜索词，过滤cookies
    let filteredCookies = cookies;
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filteredCookies = cookies.filter(cookie => 
        cookie.name.toLowerCase().includes(term) || 
        cookie.value.toLowerCase().includes(term)
      );
    }
    
    // 根据显示模式渲染
    switch (displayMode) {
      case 'list':
        renderListMode(filteredCookies);
        break;
      case 'table':
        renderTableMode(filteredCookies);
        break;
      case 'raw':
        renderRawMode(filteredCookies);
        break;
      case 'json':
        renderJsonMode(filteredCookies);
        break;
    }
  }
  
  // 列表模式渲染
  function renderListMode(cookies) {
    if (!cookieContent) return;
    
    if (!cookies || cookies.length === 0) {
      cookieContent.innerHTML = '<div style="text-align:center;padding:20px;color:var(--text-light);">没有匹配的Cookie</div>';
      return;
    }
    
    cookieContent.innerHTML = '';
    
    cookies.forEach((cookie, index) => {
      const cookieItem = document.createElement('div');
      cookieItem.className = 'cookie-item';
      cookieItem.innerHTML = `
        <span class="cookie-name">${escapeHtml(cookie.name)}</span>
        <span class="cookie-value">${escapeHtml(truncateValue(cookie.value))}</span>
      `;
      
      // 点击复制单个cookie
      cookieItem.addEventListener('click', async () => {
        await copyToClipboard(`${cookie.name}=${cookie.value}`);
        showSuccess(`Cookie "${cookie.name}" 已复制!`, 1500);
      });
      
      cookieContent.appendChild(cookieItem);
    });
  }
  
  // 表格模式渲染
  function renderTableMode(cookies) {
    if (!tableBody) return;
    
    if (!cookies || cookies.length === 0) {
      tableBody.innerHTML = '<div style="text-align:center;padding:20px;color:var(--text-light);">没有匹配的Cookie</div>';
      return;
    }
    
    tableBody.innerHTML = '';
    
    cookies.forEach((cookie, index) => {
      const row = document.createElement('div');
      row.className = 'cookie-row';
      row.innerHTML = `
        <div class="cookie-cell cookie-name-cell" title="${escapeHtml(cookie.name)}">${escapeHtml(cookie.name)}</div>
        <div class="cookie-cell cookie-value-cell" title="${escapeHtml(cookie.value)}">${escapeHtml(truncateValue(cookie.value))}</div>
        <div class="cookie-cell cookie-actions-cell">
          <button class="cookie-action copy-action" title="复制">
            <svg class="copy-icon" viewBox="0 0 24 24">
              <path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z" />
            </svg>
          </button>
          <button class="cookie-action edit-action" title="编辑">
            <svg class="edit-icon" viewBox="0 0 24 24">
              <path fill="currentColor" d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z" />
            </svg>
          </button>
          <button class="cookie-action delete-action" title="删除">
            <svg class="delete-icon" viewBox="0 0 24 24">
              <path fill="currentColor" d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z" />
            </svg>
          </button>
        </div>
      `;
      
      // 复制按钮点击
      const copyBtn = row.querySelector('.copy-action');
      if (copyBtn) {
        copyBtn.addEventListener('click', async (e) => {
          e.stopPropagation();
          await copyToClipboard(`${cookie.name}=${cookie.value}`);
          showSuccess(`Cookie "${cookie.name}" 已复制!`, 1500);
        });
      }
      
      // 编辑按钮点击
      const editBtn = row.querySelector('.edit-action');
      if (editBtn) {
        editBtn.addEventListener('click', (e) => {
          e.stopPropagation();
          showEditCookieModal(cookie);
        });
      }
      
      // 删除按钮点击
      const deleteBtn = row.querySelector('.delete-action');
      if (deleteBtn) {
        deleteBtn.addEventListener('click', async (e) => {
          e.stopPropagation();
          if (confirm(`确定要删除Cookie "${cookie.name}" 吗?`)) {
            await deleteCookie(cookie);
          }
        });
      }
      
      tableBody.appendChild(row);
    });
  }
  
  // 原始模式渲染
  function renderRawMode(cookies) {
    if (!cookieContent) return;
    
    if (!cookies || cookies.length === 0) {
      cookieContent.innerHTML = '<div style="text-align:center;padding:20px;color:var(--text-light);">没有匹配的Cookie</div>';
      return;
    }
    
    const cookieString = cookies.map(cookie => 
      `${cookie.name}=${cookie.value}`
    ).join(';\n');
    
    cookieContent.textContent = cookieString;
  }
  
  // JSON模式渲染
  function renderJsonMode(cookies) {
    if (!jsonView) return;
    
    if (!cookies || cookies.length === 0) {
      jsonView.textContent = '{\n  "cookies": []\n}';
      return;
    }
    
    const jsonObj = { cookies: cookies };
    const jsonString = JSON.stringify(jsonObj, null, 2);
    
    jsonView.textContent = jsonString;
  }
  
  // 显示添加Cookie模态框
  function showAddCookieModal() {
    if (!modalTitle || !cookieName || !cookieValue || !cookieModal) {
      console.error('添加Cookie模态框元素不存在');
      return;
    }
    
    modalTitle.textContent = '添加Cookie';
    cookieName.value = '';
    cookieValue.value = '';
    // 域名和路径已在refreshCookies中填充
    
    editingCookie = null;
    showModal();
  }
  
  // 显示编辑Cookie模态框
  function showEditCookieModal(cookie) {
    if (!modalTitle || !cookieName || !cookieValue || !cookieDomain || !cookiePath || !cookieModal) {
      console.error('编辑Cookie模态框元素不存在');
      return;
    }
    
    modalTitle.textContent = '编辑Cookie';
    cookieName.value = cookie.name;
    cookieValue.value = cookie.value;
    cookieDomain.value = cookie.domain || '';
    cookiePath.value = cookie.path || '/';
    
    editingCookie = cookie;
    showModal();
  }
  
  // 显示模态框
  function showModal() {
    if (cookieModal) {
      cookieModal.classList.add('active');
    }
  }
  
  // 隐藏模态框
  function hideModal() {
    if (cookieModal) {
      cookieModal.classList.remove('active');
    }
  }
  
  // 保存编辑的Cookie
  async function saveEditedCookie() {
    try {
      if (!cookieName || !cookieValue || !cookieDomain || !cookiePath) {
        showError('表单元素不存在');
        return;
      }
      
      const name = cookieName.value.trim();
      const value = cookieValue.value;
      const domain = cookieDomain.value.trim();
      const path = cookiePath.value.trim() || '/';
      
      if (!name) {
        showError('Cookie名称不能为空');
        return;
      }
      
      if (!domain) {
        showError('域名不能为空');
        return;
      }
      
      // 获取当前标签页
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      if (!tab || !tab.url) {
        showError('无法获取当前页面信息');
        return;
      }
      
      // 构建Cookie对象
      const cookieDetails = {
        url: tab.url,
        name: name,
        value: value,
        path: path
      };
      
      // 如果是编辑现有Cookie，先删除
      if (editingCookie) {
        await deleteCookie(editingCookie, false);
      }
      
      // 设置Cookie
      await chrome.cookies.set(cookieDetails);
      
      // 隐藏模态框
      hideModal();
      
      // 刷新列表
      await refreshCookies();
      
      // 显示成功消息
      showSuccess(`Cookie "${name}" 已${editingCookie ? '更新' : '添加'}！`);
    } catch (error) {
      console.error('保存Cookie失败:', error);
      showError(`Cookie ${editingCookie ? '更新' : '添加'}失败: ${error.message}`);
    }
  }
  
  // 删除Cookie
  async function deleteCookie(cookie, shouldRefresh = true) {
    try {
      // 获取当前标签页
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      if (!tab || !tab.url) {
        showError('无法获取当前页面信息');
        return;
      }
      
      // 删除Cookie
      await chrome.cookies.remove({
        url: tab.url,
        name: cookie.name
      });
      
      if (shouldRefresh) {
        // 刷新列表
        await refreshCookies();
        
        // 显示成功消息
        showSuccess(`Cookie "${cookie.name}" 已删除！`);
      }
    } catch (error) {
      console.error('删除Cookie失败:', error);
      showError(`删除Cookie失败: ${error.message}`);
    }
  }
  
  // 截断过长的值
  function truncateValue(value, maxLength = 30) {
    if (value.length <= maxLength) return value;
    return value.substring(0, maxLength) + '...';
  }
  
  // HTML转义
  function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }
  
  // 复制所有Cookie
  async function copyAllCookies() {
    try {
      if (!copyCookieBtn) {
        console.error('复制按钮元素不存在');
        return;
      }
      
      disableButton(copyCookieBtn, '正在复制...');
      
      // 如果没有先前获取的cookies，重新获取
      let cookies = currentCookies;
      if (!cookies || cookies.length === 0) {
        cookies = await refreshCookies();
      }
      
      // 过滤cookies（如果有搜索词）
      if (cookieSearch && cookieSearch.value) {
        const term = cookieSearch.value.toLowerCase();
        cookies = cookies.filter(cookie => 
          cookie.name.toLowerCase().includes(term) || 
          cookie.value.toLowerCase().includes(term)
        );
      }
      
      // 不同显示模式下的复制格式
      let textToCopy = '';
      
      switch(displayMode) {
        case 'json':
          textToCopy = JSON.stringify({ cookies: cookies }, null, 2);
          break;
        default:
          // 将cookie转换为字符串格式 (无换行符)
          textToCopy = cookies.map(cookie => 
            `${cookie.name}=${cookie.value}`
          ).join('; ');
      }
      
      // 复制到剪贴板
      await copyToClipboard(textToCopy);
      
      // 显示成功消息
      showSuccess(`已成功复制 ${cookies.length} 个Cookie到剪贴板！`);
    } catch (error) {
      console.error('复制Cookie失败:', error);
      showError('复制失败: ' + error.message);
    } finally {
      if (copyCookieBtn) {
        enableButton(copyCookieBtn, '复制所有Cookie');
      }
    }
  }
  
  // 复制文本到剪贴板
  async function copyToClipboard(text) {
    if (!text) {
      console.error('没有要复制的文本');
      return false;
    }
    
    try {
      await navigator.clipboard.writeText(text);
      return true;
    } catch (err) {
      console.error('复制到剪贴板失败:', err);
      
      try {
        // 回退方法
        const textarea = document.createElement('textarea');
        textarea.value = text;
        textarea.style.position = 'fixed';
        document.body.appendChild(textarea);
        textarea.select();
        const success = document.execCommand('copy');
        document.body.removeChild(textarea);
        return success;
      } catch (fallbackErr) {
        console.error('回退复制方法也失败:', fallbackErr);
        return false;
      }
    }
  }
  
  // 禁用按钮
  function disableButton(button, text) {
    if (!button) return;
    
    button.disabled = true;
    button.innerHTML = `
      <svg class="button-icon" viewBox="0 0 24 24">
        <path fill="currentColor" d="M12,4V2A10,10 0 0,0 2,12H4A8,8 0 0,1 12,4Z">
          <animateTransform 
            attributeName="transform" 
            attributeType="XML" 
            type="rotate"
            dur="1s" 
            from="0 12 12"
            to="360 12 12" 
            repeatCount="indefinite" />
        </path>
      </svg>
      ${text}
    `;
  }
  
  // 启用按钮
  function enableButton(button, text) {
    if (!button) return;
    
    button.disabled = false;
    button.innerHTML = `
      <svg class="button-icon" viewBox="0 0 24 24">
        <path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z" />
      </svg>
      ${text}
    `;
  }
  
  // 显示成功消息
  function showSuccess(message, duration = 3000) {
    if (!status) return;
    
    status.textContent = message;
    status.className = 'status-success';
    status.style.display = 'block';
    
    setTimeout(() => {
      if (status) {
        status.style.display = 'none';
      }
    }, duration);
  }
  
  // 显示错误消息
  function showError(message, duration = 3000) {
    if (!status) return;
    
    status.textContent = message;
    status.className = 'status-error';
    status.style.display = 'block';
    
    setTimeout(() => {
      if (status) {
        status.style.display = 'none';
      }
    }, duration);
  }
  
  // 在cookieContent顶部插入参数区（支持飞书多维表格和扣子工作流）
  function renderParamsArea(paramList) {
    if (!cookieContent) return;
    // 移除已存在的参数区
    const old = document.getElementById('feishuParamsArea');
    if (old) old.remove();
    if (!paramList || paramList.length === 0) return;
    // 构建参数区
    const area = document.createElement('div');
    area.id = 'feishuParamsArea';
    area.className = 'feishu-params-area';
    area.innerHTML = paramList.map(param =>
      `<div class="feishu-param-row">
        <span class="feishu-param-label">${param.label}：</span>
        <span class="feishu-param-value" title="${param.value}">${param.value || '-'}</span>
        <button class="feishu-param-copy-btn" data-value="${param.value || ''}" title="复制${param.label}">
          <svg class="copy-icon" viewBox="0 0 24 24"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"/></svg>
        </button>
      </div>`
    ).join('');
    cookieContent.prepend(area);
    // 绑定复制事件
    area.querySelectorAll('.feishu-param-copy-btn').forEach(btn => {
      btn.addEventListener('click', async (e) => {
        const value = btn.getAttribute('data-value');
        if (value) {
          await copyToClipboard(value);
          showSuccess(`已复制: ${value}`);
        }
      });
    });
  }
});