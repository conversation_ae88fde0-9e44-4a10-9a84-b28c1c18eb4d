# Cookie复制工具

## 插件介绍

这是一个Chrome浏览器扩展，用于方便地查看、复制、编辑和管理当前网页的Cookie信息。插件提供了美观的用户界面和多种实用功能，特别适合开发人员和测试人员使用。

## 主要功能

1. **查看Cookie**：以列表、表格、原始文本或JSON格式查看当前页面的所有Cookie
2. **复制Cookie**：一键复制所有Cookie或单个Cookie
3. **搜索过滤**：根据名称或值快速筛选Cookie
4. **编辑管理**：添加、修改或删除Cookie
5. **统计信息**：显示Cookie数量和总大小
6. **深色模式**：支持切换深色/浅色主题
7. **页面类型识别**：自动识别小红书和抖音网站的不同页面类型（新增功能）
8. **多平台支持**：支持识别B站、政府网站、高校就业网站、微信公众号、飞书和扣子平台等多种网站
9. **飞书多维表格参数识别与展示**：自动识别飞书多维表格页面，在Cookie列表上方明文展示 app_token、table_id、view 参数，每项均可单独一键复制，同时在主操作区也有快捷复制按钮。

## 文件结构

```
├── manifest.json      # 插件配置文件
├── popup.html        # 插件弹出窗口界面
├── popup.js          # 插件主要功能实现
├── icons/            # 插件图标资源
├── *.txt            # 各平台URL分析数据
└── *.md             # 各平台分析报告文档
```

## 页面类型识别功能

新增功能可以自动识别小红书、抖音、B站以及政府网站、高校就业网站、微信公众号、飞书和扣子平台等多种网站的不同页面类型，包括：

### 小红书页面类型

- 小红书笔记详情页
- 小红书搜索页
- 小红书博主主页
- 小红书通知页
- 小红书发现页（细分类型识别）
  - 推荐
  - 穿搭
  - 美食
  - 彩妆
  - 影视
  - 职场
  - 情感
  - 家居
  - 游戏
  - 旅行
  - 健身
  - 其他

### 抖音页面类型

- 抖音主页
- 抖音精选页（细分类型识别）
  - 全部
  - 知识
  - 体育
  - 汽车
  - 二次元
  - 游戏
  - 影视
  - 生活vlog
  - 旅行
  - 小剧场
  - 美食
  - 三农
  - 音乐
  - 动物
  - 亲子
  - 美妆
  - 其他
- 抖音推荐页
- 抖音关注页
- 抖音朋友页
- 抖音博主主页
- 抖音直播页（细分类型识别）
  - 射击游戏
  - 竞技游戏
  - 单机游戏
  - 棋牌游戏
  - 休闲益智
  - 角色扮演
  - 策略卡牌
  - 娱乐天地
  - 科技文化
  - 其他分类
- 抖音放映厅
- 抖音短剧
- 抖音搜索页（细分类型识别）
  - 综合
  - 视频
  - 用户
  - 直播
  - 其他
- 抖音视频详情页

### B站页面类型

- B站主页
- B站搜索综合区（细分排序方式）
  - 综合排序
  - 最多播放
  - 最新发布
  - 最多弹幕
  - 最多收藏
  - 其他排序
- B站搜索视频区（细分排序方式）
  - 综合排序
  - 最多播放
  - 最新发布
  - 最多弹幕
  - 最多收藏
  - 其他排序
- B站搜索专栏区（细分排序方式）
  - 综合排序
  - 最新发布
  - 最多点击
  - 最多喜欢
  - 最多评论
  - 其他排序
- B站搜索用户区（细分排序方式）
  - 默认排序
  - 粉丝数由高到低
  - 粉丝数由低到高
  - Lv等级由高到低
  - Lv等级由低到高
  - 其他排序
- B站视频详情页
- 其他B站页面

识别结果会显示在插件界面上，帮助用户了解当前所处的页面类型，便于针对性地管理Cookie。

## 使用方法

1. 在Chrome浏览器中安装此扩展
2. 访问任意网页
3. 点击工具栏中的插件图标打开插件
4. 查看、搜索、复制或管理Cookie
5. 插件会自动识别并显示当前页面类型（支持小红书、抖音、B站等多个平台）

## 技术实现

### 页面类型识别实现

插件通过分析URL结构来识别不同网站的页面类型：

1. **小红书页面识别**：通过分析URL路径和查询参数，识别不同类型的小红书页面，如笔记详情页、发现页、搜索页等。对于发现页，进一步通过`channel_id`参数识别具体的内容分类。

2. **抖音页面识别**：通过分析域名、URL路径和查询参数，识别不同类型的抖音页面。例如：
   - 通过域名区分主站和直播站
   - 通过路径前缀识别精选页、视频页、用户页等
   - 通过查询参数识别搜索类型、推荐页等
   - 通过路径模式匹配识别直播分类

3. **政府网站识别**：识别国资委相关网站的不同页面类型：
   - 国资委官网（中央、省级、市级）的首页、招聘专栏和文章详情页
   - 通过URL路径中的特定模式（如`/n数字/`、`/channels/`、`/articles/`等）识别页面类型
   - 通过路径结构（如`/content.html`、`/index.html`）区分不同功能页面

4. **高校就业网站识别**：识别高校就业信息网的不同页面类型：
   - 山东高校毕业生就业网、山东大学和山东财经大学就业信息网等
   - 区分首页、招聘专栏和文章详情页
   - 通过URL路径（如`/campus`、`/col/jysc/`）和查询参数识别页面功能

5. **其他平台识别**：
   - 微信公众号文章页面
   - 飞书平台
     - 云文档（知识库、普通文档）
     - 多维表格（普通多维表格、知识库中的多维表格）
       - 自动识别并明文展示 app_token、table_id 和 view 参数
       - 每项参数均可单独一键复制
       - 参数区位于Cookie列表上方，风格与主界面协调
       - 主操作区也有横向快捷复制按钮
       - 适配普通多维表格和知识库多维表格两种URL格式
     - 表格
   - 扣子平台
     - 通过URL路径识别不同类型：
       - `/home` 路径识别为主页
       - `/work_flow` 路径配合 `workflow_id` 参数识别为工作流
       - `/space/{space_id}/bot/` 路径识别为智能体
       - `/space/{space_id}/project-ide/` 路径识别为应用
       - `/space/{space_id}/develop` 路径识别为工作空间项目开发
       - `/space/{space_id}/library` 路径识别为工作空间资源库
       - `/space/{space_id}/publish` 路径识别为工作空间发布管理
       - `/space/{space_id}/model` 路径识别为工作空间模型管理
       - `/space/{space_id}/evaluate` 路径识别为工作空间效果评估
       - `/space/{space_id}/team` 路径识别为工作空间成员与设置
     - 扣子工作空间
       - 项目开发
       - 资源库
       - 发布管理
         - 智能体发布（/space/{space_id}/publish?tab=agent）
         - 应用发布（/space/{space_id}/publish?tab=app）
         - 工作流发布（/space/{space_id}/publish?tab=workflow）
       - 模型管理
       - 效果评估
       - 成员与设置
     - 其他页面

- 使用不同的图标区分各类页面
- 通过URL参数和路径组合实现精确识别

- 使用Chrome Extension API获取和管理Cookie
- 纯原生JavaScript实现，无需额外依赖
- 响应式设计，支持深色模式
- 使用正则表达式识别URL模式，判断页面类型

## 隐私说明

本插件仅在用户主动点击时获取当前页面的Cookie信息，不会自动收集或上传任何数据。所有操作均在本地完成，不涉及网络请求。

## 开发者信息

插件由姚安开发，如有问题或建议，请通过插件中提供的联系方式反馈。

## 更新日志

### 最新版本
- 新增B站页面类型识别功能
- 新增政府网站、高校就业网站识别功能
- 新增微信公众号、飞书、扣子平台识别功能
- 优化页面类型识别算法
- 改进用户界面交互体验

### 飞书平台页面类型与参数展示

- 云文档（知识库、普通文档）
- 多维表格（普通多维表格、知识库中的多维表格）
  - 自动识别并明文展示 app_token、table_id 和 view 参数
  - 每项参数均可单独一键复制
  - 参数区位于Cookie列表上方，风格与主界面协调
  - 主操作区也有横向快捷复制按钮
  - 适配普通多维表格和知识库多维表格两种URL格式
- 表格

> 建议配合截图展示参数区与按钮区的实际效果，便于用户理解交互。