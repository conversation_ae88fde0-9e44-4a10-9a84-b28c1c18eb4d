# 其他网站URL结构分析报告

## 概述

通过对其他网站.txt中的URL进行分析，我们可以识别出多个政府和教育类网站的不同页面类型及其URL结构特点。这些特点可以用于开发自动识别页面类型的功能。

## 网站类型分类

### 1. 国资委网站

**域名特征**：`www.sasac.gov.cn`

**页面类型**：
- **官网首页**：`http://www.sasac.gov.cn`
- **招聘专栏**：URL包含`/n2588035/n2588325/n2588350/index.html`
- **文章详情页**：URL包含`/content.html`，前面有多级目录结构（如`/n2588035/n2588325/n2588350/c33238107/content.html`）

**URL结构特点**：
- 使用`/n`开头的数字编码作为目录结构
- 文章详情页使用`/c`开头的数字编码作为文章ID
- 详情页以`/content.html`结尾

### 2. 山东省国资委网站

**域名特征**：`gzw.shandong.gov.cn`

**页面类型**：
- **官网首页**：`http://gzw.shandong.gov.cn/`
- **招聘专栏**：URL包含`/channels/ch00223/`
- **文章详情页**：URL格式为`/articles/ch00223/年月/UUID.html`

**URL结构特点**：
- 使用`/channels/`表示栏目页
- 使用`/articles/`表示文章页
- 文章URL包含年月信息和UUID格式的唯一标识符

### 3. 山东高校毕业生就业网

**域名特征**：`www.sdgxbys.cn`

**页面类型**：
- **官网首页**：`https://www.sdgxbys.cn/`
- **国企招聘专栏**：URL包含`/col/jysc/index.html?cn=gqzp`
- **文章详情页**：URL格式为`/art/gqzp/[ID].html`

**URL结构特点**：
- 使用`/col/`表示栏目页
- 使用`/art/`表示文章页
- 查询参数`cn=gqzp`表示国企招聘类别

### 4. 济南市国资委网站

**域名特征**：`jngzw.jinan.gov.cn`

**页面类型**：
- **官网首页**：`http://jngzw.jinan.gov.cn/`
- **招聘专栏**：URL包含`/col/col23870/index.html`
- **文章详情页**：URL格式为`/art/年/月/日/art_栏目ID_文章ID.html`

**URL结构特点**：
- 使用`/col/col数字/`表示栏目页
- 使用`/art/`表示文章页
- 文章URL包含年月日信息和文章ID

### 5. 高校就业信息网

#### 5.1 山东大学就业信息网

**域名特征**：`job.sdu.edu.cn`

**页面类型**：
- **官网首页**：`https://job.sdu.edu.cn/`

#### 5.2 山东财经大学就业信息网

**域名特征**：`jobsdufe.sdbys.com`

**页面类型**：
- **官网首页**：`https://jobsdufe.sdbys.com/`
- **招聘专栏**：URL包含`/campus`
- **文章详情页**：URL格式为`/campus/view/id/数字ID`

**URL结构特点**：
- 使用`/campus`表示校园招聘栏目
- 使用`/view/id/`加数字ID表示文章详情页

### 6. 微信公众号文章

**域名特征**：`mp.weixin.qq.com`

**页面类型**：
- **文章页**：URL以`/s`开头，或包含复杂的查询参数

**URL结构特点**：
- 简短URL格式：`https://mp.weixin.qq.com/s/[ID]`
- 完整URL格式：包含`__biz`、`mid`、`idx`、`sn`等多个查询参数

### 7. 飞书文档

**域名特征**：`*.feishu.cn`

**页面类型**：
- **云文档**：URL包含`/wiki/`、`/docx/`
- **多维表格**：URL包含`/base/`
- **表格**：URL包含`/sheets/`

**URL结构特点**：
- 使用不同的路径前缀区分文档类型
- 包含复杂的查询参数如`table`、`view`、`from`等

### 8. 扣子平台

**域名特征**：`www.coze.cn`、`space.coze.cn`

**页面类型**：
- **平台首页**：`https://www.coze.cn`
- **空间页**：`https://space.coze.cn`
- **工作流**：URL包含`/work_flow`和`space_id`、`workflow_id`参数

**URL结构特点**：
- 使用查询参数`space_id`和`workflow_id`标识具体工作流

## 识别规则总结

基于以上分析，我们可以通过以下规则识别不同网站的页面类型：

1. **域名匹配**：首先通过域名识别网站类别
2. **路径模式**：分析URL路径中的特定模式（如`/col/`、`/art/`、`/wiki/`等）
3. **查询参数**：分析URL查询参数中的特定键值对
4. **ID格式**：分析URL中的ID格式和位置

这些规则可以用于开发自动识别页面类型的功能，帮助用户更好地管理和使用Cookie。